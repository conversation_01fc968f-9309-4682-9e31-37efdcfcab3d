name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: testuser
          POSTGRES_PASSWORD: testpass
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Create test environment file
      run: |
        cat > .env.test.local << EOF
        NODE_ENV=test
        DATABASE_URL=postgresql://testuser:testpass@localhost:5432/testdb
        JWT_SECRET=test-jwt-secret-key-for-testing-purposes-only
        JWT_EXPIRES_IN=1h
        BCRYPT_SALT_ROUNDS=4
        PORT=3001
        ORIGIN=http://localhost:3001
        CREDENTIALS=true
        LOG_FORMAT=combined
        LOG_DIR=../test-logs
        LOG_LEVEL=error
        RATE_LIMIT_WINDOW_MS=60000
        RATE_LIMIT_MAX=1000
        RATE_LIMIT_MESSAGE=Too many test requests
        DB_HOST=localhost
        DB_PORT=5432
        DB_DATABASE=testdb
        DB_USERNAME=testuser
        DB_PASSWORD=testpass
        SECRET_KEY=test-jwt-secret-key-for-testing-purposes-only
        EOF

    - name: Run database migrations
      run: npm run db:migrate:test
      env:
        DATABASE_URL: postgresql://testuser:testpass@localhost:5432/testdb

    - name: Run tests
      run: npm test
      env:
        NODE_ENV: test
        DATABASE_URL: postgresql://testuser:testpass@localhost:5432/testdb

    - name: Run tests with coverage
      run: npm run testAll
      env:
        NODE_ENV: test
        DATABASE_URL: postgresql://testuser:testpass@localhost:5432/testdb

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      if: matrix.node-version == '20.x'
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

# Backend Architecture

This document outlines the architectural style used in this backend implementation. The goal is to provide a clear understanding of the structure and conventions, enabling developers to build new features consistently and maintainably.

## Architectural Style: Layered Architecture (N-Tier)

The backend is built using a **Layered Architecture**. This pattern separates the application into distinct layers, each with a specific responsibility. This separation of concerns makes the application easier to understand, develop, test, and maintain.

The layers are organized as follows, with dependencies flowing in one direction (e.g., Routes depend on Controllers, which depend on Services):

1.  **Routes Layer**: The entry point for all HTTP requests.
2.  **Controllers Layer**: Handles request/response logic and input validation.
3.  **Services Layer**: Contains the core business logic.
4.  **Data Access Layer**: Interacts with the database.

---

## Project Structure and Layer Responsibilities

The project's directory structure reflects the layered architecture:

```
src/
├── app.ts                  # Main application setup (Express)
├── server.ts               # Server initialization
├── config/                 # Application configuration
├── controllers/            # Controllers Layer
├── dtos/                   # Data Transfer Objects for validation
├── exceptions/             # Custom exception classes
├── interfaces/             # TypeScript interfaces
├── loaders/                # Loaders for external services (e.g., Prisma)
├── middlewares/            # Express middlewares
├── prisma/                 # Database schema and migrations
├── routes/                 # Routes Layer
├── services/               # Services Layer
├── test/                   # Automated tests
└── utils/                  # Utility functions
```

### 1. Routes Layer (`src/routes`)

*   **Responsibility**: Defines the API endpoints (e.g., `/users`, `/auth`). It maps HTTP methods (GET, POST, PUT, DELETE) and URLs to specific controller methods.
*   **Implementation**: Each feature (e.g., `users`, `auth`) has its own route file (e.g., `users.route.ts`). These files use the Express Router to define the routes.

### 2. Controllers Layer (`src/controllers`)

*   **Responsibility**: Acts as the intermediary between the routes and services. It receives the request object from the route, extracts relevant information (e.g., request body, path parameters), validates the input using DTOs, and calls the appropriate service method to handle the business logic. It then formats the response and sends it back to the client.
*   **Implementation**: Each feature has a corresponding controller file (e.g., `users.controller.ts`).

### 3. Services Layer (`src/services`)

*   **Responsibility**: This is the core of the application. It contains the business logic, algorithms, and orchestrates interactions with the data access layer and other services. It is completely decoupled from the HTTP layer (i.e., it doesn't know about `req` or `res` objects).
*   **Implementation**: Each feature has a service file (e.g., `users.service.ts`).

### 4. Data Access Layer (`src/prisma`, `src/repositories`)

*   **Responsibility**: Handles all communication with the database. It is responsible for creating, reading, updating, and deleting data. This layer abstracts the database logic from the services, making the application more modular and easier to test.
*   **Implementation**: This project uses **Prisma** as its Object-Relational Mapper (ORM). The database schema is defined in `src/prisma/schema.prisma`.
    *   **Repositories (`src/repositories`)**: The services interact with repositories (e.g., `user.repository.ts`) instead of directly with Prisma Client. Repositories encapsulate database queries, providing a clear and reusable API for data access. This pattern allows for easier mocking in tests and centralizes database logic.

---

## Supporting Components

### Data Transfer Objects (DTOs) (`src/dtos`)

*   **Purpose**: DTOs are classes that define the expected structure of data for incoming requests. They are used in conjunction with `class-validator` and `class-transformer` to automatically validate the request body. This ensures that the controllers and services receive data in the correct format.

### Middlewares (`src/middlewares`)

*   **Purpose**: Middlewares are functions that execute before the request reaches the controller. They are used for handling cross-cutting concerns that affect multiple routes, such as:
    *   **Authentication**: Verifying user identity.
    *   **Authorization**: Checking user permissions.
    *   **Error Handling**: Catching and formatting errors.
    *   **Logging**: Recording request information.
    *   **Input Validation**: Can also be implemented as a middleware.

### Interfaces (`src/interfaces`)

*   **Purpose**: Defines TypeScript interfaces for data structures and class contracts. This helps to enforce consistency and improve type safety throughout the application.

### Loaders (`src/loaders`)

*   **Purpose**: Loaders are responsible for initializing and configuring services and connections that the application depends on, such as the database connection (Prisma) or other external services. They are typically run once when the application starts.

---

## Request Flow

A typical request flows through the application as follows:

1.  An HTTP request arrives at the **Express server**.
2.  The request is passed through any relevant **Middlewares** (e.g., for logging, authentication).
3.  The **Routes Layer** matches the request URL and HTTP method to a specific controller action.
4.  The **Controllers Layer** receives the request. It uses a **DTO** to validate the request body.
5.  The controller calls the appropriate method in the **Services Layer**, passing the validated data.
6.  The **Services Layer** executes the business logic. If database access is needed, it uses the **Prisma Client** to interact with the database.
7.  The service returns the result to the controller.
8.  The controller formats the result into an HTTP response and sends it back to the client.
9.  If any errors occur during this process, the **Error Handling Middleware** catches them and sends a standardized error response.

---

## How to Add a New Feature

To add a new feature (e.g., "products"), you would follow these steps:

1.  **Database**: If needed, update `src/prisma/schema.prisma` with the new `Product` model and run `npx prisma migrate dev` to update the database.
2.  **Interface**: Create `src/interfaces/products.interface.ts` to define the `Product` type.
3.  **DTO**: Create `src/dtos/products.dto.ts` to define the DTO for creating/updating a product, including validation rules.
4.  **Service**: Create `src/services/products.service.ts` to implement the business logic for product operations (e.g., `createProduct`, `getProductById`).
5.  **Controller**: Create `src/controllers/products.controller.ts` to handle the HTTP requests, validate input with the DTO, and call the `ProductsService`.
6.  **Route**: Create `src/routes/products.route.ts` to define the API endpoints for products (e.g., `POST /products`, `GET /products/:id`).
7.  **Integration**: Add the new route to the `routes` array in `src/server.ts`.
8.  **Testing**: Add unit and integration tests for the new feature in the `src/test` directory.

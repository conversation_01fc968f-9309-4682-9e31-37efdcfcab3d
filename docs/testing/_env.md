# Environment Configuration Testing

**Title:** Environment Configuration Testing Guide  
**Description:** Testing documentation for environment-specific configurations, including development, staging, and production environment setups, configuration validation, environment variable management, and deployment testing.

# Environment Variables Reference

Here is a comprehensive list of environment variables used across the Secure Backend project, along with their descriptions and default values.

## Testing Variables

For cURL testing examples, use these environment variables:

```bash
# Base URLs for API testing
export BASE_URL="http://localhost:3000/api/v1"
export AUTH_BASE_URL="http://localhost:3000/api/v1/auth"
export USERS_BASE_URL="http://localhost:3000/api/v1/users"
export HEALTH_URL="http://localhost:3000/health"

# Cookie jar for session management
export COOKIE_JAR="~/cookies.txt"
export ADMIN_COOKIE_JAR="~/admin_cookies.txt"

# Test authentication tokens (set after login)
export USER_JWT_TOKEN="your_jwt_token_here"
export ADMIN_JWT_TOKEN="admin_jwt_token_here"

# Test user credentials
export TEST_USER_EMAIL="<EMAIL>"
export TEST_USER_PASSWORD="SecurePassword123!"
export ADMIN_USER_EMAIL="<EMAIL>"
export ADMIN_USER_PASSWORD="AdminPassword123!"

# Common headers
export CONTENT_TYPE="application/json"
export USER_AGENT="cURL-Testing/1.0"
```

## Core Configuration

- `NODE_ENV`: Specifies the running environment (e.g., development, test, production). Defaults to `development`.
- `PORT`: The port on which the server listens. Defaults to `3000`.
- `ORIGIN`: The allowed origin for CORS requests. Defaults to `*`.
- `CREDENTIALS`: A boolean indicating if credentials are included in CORS requests. Defaults to `true`.

## Database Configuration

- `DATABASE_URL`: Connection string for the PostgreSQL database.
- `DB_HOST`, `DB_PORT`, `DB_DATABASE`, `DB_USERNAME`, `DB_PASSWORD`: Legacy fields for database configuration.

## JWT Configuration

- `JWT_SECRET`: Secret key for JWT token generation.
- `JWT_EXPIRES_IN`: JWT access token expiration time. Defaults to `15m`.
- `JWT_REFRESH_EXPIRES_IN`: JWT refresh token expiration time. Defaults to `30d`.

## Password Hashing Configuration

- `BCRYPT_SALT_ROUNDS`: Number of salt rounds for bcrypt password hashing. Defaults to `12`.

## Logging Configuration

- `LOG_FORMAT`: Format of the log output (e.g., dev, combined). Defaults to `dev`.
- `LOG_DIR`: Directory for log files. Defaults to `../logs`.

## Rate Limiting Configuration

- `RATE_LIMIT_WINDOW_MS`: Rate limit window in milliseconds (15 minutes).
- `RATE_LIMIT_MAX`: Maximum requests per window. Defaults to `100`.
- `RATE_LIMIT_MESSAGE`: Message displayed when rate limit is exceeded.

## Security Headers

- Integrated using Helmet.js for added security.

## Token Storage

- Access and refresh tokens are stored in secure HTTP-only cookies:
  - `accessToken`: Short-lived access token stored as a cookie.
  - `refreshToken`: Long-lived refresh token stored as a cookie.

Ensure any sensitive values like `DATABASE_URL` and `JWT_SECRET` are appropriately secured and not exposed in any public or insecure locations.

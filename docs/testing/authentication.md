# Authentication Testing Guide

**Title:** Authentication Testing Guide with cURL Examples  
**Description:** Comprehensive testing documentation for authentication mechanisms using cURL commands, including registration, login/logout flows, token refresh, and cookie-based session management.

## Overview

This guide demonstrates how to test the authentication system using cURL commands. The system uses cookies-only authentication where tokens are stored in secure HTTP-only cookies rather than being returned in JSON responses.

> **See Authentication Guide prerequisites:** Refer to [Environment Configuration Guide](_env.md) for setting up testing variables.

## Prerequisites

1. Ensure the server is running on port 3000
2. Have cURL installed and available in your terminal
3. Set up environment variables as defined in [_env.md](_env.md)
4. Create a cookie jar file for session management

```bash
# Load environment variables from _env.md
source <(grep '^export' docs/testing/_env.md | sed 's/^export /export /')

# Create cookie jar files for testing
touch $COOKIE_JAR
touch $ADMIN_COOKIE_JAR
```

---

## 1. User Registration

### Successful Registration (201)

```bash
curl -X POST $AUTH_BASE_URL/register \
  -H "Content-Type: $CONTENT_TYPE" \
  -H "User-Agent: $USER_AGENT" \
  -c $COOKIE_JAR \
  -d '{
    "email": "'$TEST_USER_EMAIL'",
    "password": "'$TEST_USER_PASSWORD'"
  }'
```

**Expected Response (201 Created):**
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "clm2xyz123abc",
      "email": "<EMAIL>",
      "role": "USER",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    },
    "expiresIn": "15m",
    "tokenType": "Bearer"
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

**Cookies Set:**
- `accessToken` (HTTP-only, Secure, SameSite=Strict, 15min TTL)
- `refreshToken` (HTTP-only, Secure, SameSite=Strict, 30day TTL)

### Duplicate Email Registration (409)

```bash
curl -X POST $AUTH_BASE_URL/register \
  -H "Content-Type: $CONTENT_TYPE" \
  -H "User-Agent: $USER_AGENT" \
  -c $COOKIE_JAR \
  -d '{
    "email": "'$TEST_USER_EMAIL'",
    "password": "AnotherPassword123!"
  }'
```

**Expected Response (409 Conflict):**
```json
{
  "success": false,
  "message": "User already exists with this email",
  "statusCode": 409,
  "timestamp": "2024-01-15T10:31:00.000Z"
}
```

### Registration with Admin Role

```bash
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -H "User-Agent: cURL-Testing/1.0" \
  -c ~/cookies.txt \
  -d '{
    "email": "<EMAIL>",
    "password": "AdminPassword123!",
    "role": "ADMIN"
  }'
```

---

## 2. User Login

### Successful Login (200)

```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -H "User-Agent: cURL-Testing/1.0" \
  -c ~/cookies.txt \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePassword123!"
  }'
```

**Expected Response (200 OK):**
```json
{
  "success": true,
  "message": "User logged in successfully",
  "data": {
    "user": {
      "id": "clm2xyz123abc",
      "email": "<EMAIL>",
      "role": "USER",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    },
    "expiresIn": "15m",
    "tokenType": "Bearer"
  },
  "timestamp": "2024-01-15T10:32:00.000Z"
}
```

### Invalid Credentials (401)

```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -H "User-Agent: cURL-Testing/1.0" \
  -c ~/cookies.txt \
  -d '{
    "email": "<EMAIL>",
    "password": "WrongPassword123!"
  }'
```

**Expected Response (401 Unauthorized):**
```json
{
  "success": false,
  "message": "Invalid credentials",
  "statusCode": 401,
  "timestamp": "2024-01-15T10:33:00.000Z"
}
```

---

## 3. Protected Route Access

### Get User Profile (With Valid Session)

```bash
curl -X GET http://localhost:3000/api/auth/profile \
  -H "Content-Type: application/json" \
  -H "User-Agent: cURL-Testing/1.0" \
  -b ~/cookies.txt
```

**Expected Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "id": "clm2xyz123abc",
    "email": "<EMAIL>",
    "role": "USER",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

### Access Without Cookies (401)

```bash
curl -X GET http://localhost:3000/api/auth/profile \
  -H "Content-Type: application/json" \
  -H "User-Agent: cURL-Testing/1.0"
```

**Expected Response (401 Unauthorized):**
```json
{
  "success": false,
  "message": "Access token is required",
  "statusCode": 401,
  "timestamp": "2024-01-15T10:34:00.000Z"
}
```

---

## 4. Token Refresh (Silent Refresh Flow)

### Successful Token Refresh

```bash
curl -X POST http://localhost:3000/api/auth/refresh \
  -H "Content-Type: application/json" \
  -H "User-Agent: cURL-Testing/1.0" \
  -b ~/cookies.txt \
  -c ~/cookies.txt
```

**Expected Response (200 OK):**
```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "data": {
    "user": {
      "id": "clm2xyz123abc",
      "email": "<EMAIL>",
      "role": "USER",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    },
    "expiresIn": "15m",
    "tokenType": "Bearer"
  },
  "timestamp": "2024-01-15T10:35:00.000Z"
}
```

**Note:** New tokens are automatically set as cookies. Both `-b` (read cookies) and `-c` (write cookies) are used.

### Refresh Without Valid Refresh Token

```bash
curl -X POST http://localhost:3000/api/auth/refresh \
  -H "Content-Type: application/json" \
  -H "User-Agent: cURL-Testing/1.0"
```

**Expected Response (400 Bad Request):**
```json
{
  "success": false,
  "message": "Refresh token is required",
  "statusCode": 400,
  "timestamp": "2024-01-15T10:36:00.000Z"
}
```

---

## 5. User Logout

### Successful Logout

```bash
curl -X POST http://localhost:3000/api/auth/logout \
  -H "Content-Type: application/json" \
  -H "User-Agent: cURL-Testing/1.0" \
  -b ~/cookies.txt \
  -c ~/cookies.txt
```

**Expected Response (200 OK):**
```json
{
  "success": true,
  "message": "Logged out successfully",
  "timestamp": "2024-01-15T10:37:00.000Z"
}
```

**Cookies Cleared:**
- `accessToken` cookie is removed
- `refreshToken` cookie is removed
- Tokens are added to server-side blacklist

### Access After Logout (401)

```bash
curl -X GET http://localhost:3000/api/auth/profile \
  -H "Content-Type: application/json" \
  -H "User-Agent: cURL-Testing/1.0" \
  -b ~/cookies.txt
```

**Expected Response (401 Unauthorized):**
```json
{
  "success": false,
  "message": "Access token is required",
  "statusCode": 401,
  "timestamp": "2024-01-15T10:38:00.000Z"
}
```

---

## 6. Advanced Authentication Scenarios

### CSRF Protection Testing

For applications with CSRF protection enabled, include the CSRF token:

```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -H "User-Agent: cURL-Testing/1.0" \
  -H "X-CSRF-Token: csrf_token_here" \
  -c ~/cookies.txt \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePassword123!"
  }'
```

### Custom Authorization Headers

For API clients that need to send authorization headers along with cookies:

```bash
curl -X GET http://localhost:3000/api/auth/profile \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer custom_api_key" \
  -H "User-Agent: MyApp/1.0" \
  -b ~/cookies.txt
```

### Rate Limiting Testing

Test rate limiting by making multiple rapid requests:

```bash
for i in {1..6}; do
  echo "Request $i:"
  curl -X POST http://localhost:3000/api/auth/login \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"wrong"}' \
    -w "HTTP Status: %{http_code}\n" \
    -o /dev/null -s
  sleep 1
done
```

---

## 7. End-to-End Workflow Example

### Complete Authentication Flow

```bash
#!/bin/bash

# Step 1: Create cookie jar
COOKIE_JAR="~/auth_test_cookies.txt"
rm -f $COOKIE_JAR
touch $COOKIE_JAR

echo "=== 1. User Registration ==="
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -c $COOKIE_JAR \
  -d '{
    "email": "<EMAIL>",
    "password": "WorkflowTest123!"
  }' | jq .

echo "\n=== 2. Access Protected Resource ==="
curl -X GET http://localhost:3000/api/auth/profile \
  -H "Content-Type: application/json" \
  -b $COOKIE_JAR | jq .

echo "\n=== 3. Refresh Token ==="
curl -X POST http://localhost:3000/api/auth/refresh \
  -H "Content-Type: application/json" \
  -b $COOKIE_JAR \
  -c $COOKIE_JAR | jq .

echo "\n=== 4. Update Profile ==="
curl -X PATCH http://localhost:3000/api/auth/profile \
  -H "Content-Type: application/json" \
  -b $COOKIE_JAR \
  -d '{
    "email": "<EMAIL>"
  }' | jq .

echo "\n=== 5. Logout ==="
curl -X POST http://localhost:3000/api/auth/logout \
  -H "Content-Type: application/json" \
  -b $COOKIE_JAR \
  -c $COOKIE_JAR | jq .

echo "\n=== 6. Verify Logout (Should Fail) ==="
curl -X GET http://localhost:3000/api/auth/profile \
  -H "Content-Type: application/json" \
  -b $COOKIE_JAR | jq .

# Cleanup
rm -f $COOKIE_JAR
```

---

## 8. Cookie Management Explained

### Cookie Jar Usage

- **`-c ~/cookies.txt`**: Write received cookies to file
- **`-b ~/cookies.txt`**: Send cookies from file with request
- **`-c ~/cookies.txt -b ~/cookies.txt`**: Both read and write cookies

### Cookie Inspection

```bash
# View cookies in the jar
cat ~/cookies.txt

# Example cookie file format:
# Netscape HTTP Cookie File
# localhost	FALSE	/	FALSE	1705402200	accessToken		eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
# localhost	FALSE	/	FALSE	1707822200	refreshToken		eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Security Properties

The cookies set by this system have the following security properties:

- **HttpOnly**: Cannot be accessed via JavaScript (XSS protection)
- **Secure**: Only sent over HTTPS in production (MITM protection)
- **SameSite=Strict**: CSRF protection
- **Path=/**: Available to all routes
- **TTL**: Access token (15min), Refresh token (30 days)

---

## 9. Testing Different Scenarios

### Password Validation

```bash
# Test weak password (should fail)
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "123"
  }'
```

### Email Validation

```bash
# Test invalid email format (should fail)
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "invalid-email",
    "password": "ValidPassword123!"
  }'
```

### Missing Fields

```bash
# Test missing password (should fail)
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

---

## 10. Error Response Formats

All error responses follow this consistent format:

```json
{
  "success": false,
  "message": "Error description",
  "statusCode": 400,
  "timestamp": "2024-01-15T10:39:00.000Z",
  "errors": [  // Optional validation errors array
    {
      "field": "email",
      "message": "Invalid email format"
    }
  ]
}
```

---

## 11. Admin-Only Routes Testing

### Get All Users (Admin Required)

```bash
# First login as admin
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -c ~/admin_cookies.txt \
  -d '{
    "email": "<EMAIL>",
    "password": "AdminPassword123!"
  }'

# Then access admin route
curl -X GET http://localhost:3000/api/auth/users \
  -H "Content-Type: application/json" \
  -b ~/admin_cookies.txt
```

### Update User Role (Admin Required)

```bash
curl -X PATCH http://localhost:3000/api/auth/users/clm2xyz123abc/role \
  -H "Content-Type: application/json" \
  -b ~/admin_cookies.txt \
  -d '{
    "role": "MODERATOR"
  }'
```

---

## 12. Performance and Load Testing

### Concurrent Login Testing

```bash
# Test concurrent logins (requires GNU parallel)
seq 1 10 | parallel -j10 curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"SecurePassword123!"}' \
  -w "Response time: %{time_total}s, Status: %{http_code}\n" \
  -o /dev/null -s
```

### Memory Leak Detection

```bash
# Monitor server memory during heavy authentication load
for i in {1..100}; do
  curl -X POST http://localhost:3000/api/auth/login \
    -H "Content-Type: application/json" \
    -c "/tmp/cookie_$i.txt" \
    -d '{"email":"<EMAIL>","password":"SecurePassword123!"}' \
    -o /dev/null -s &
done
wait
```

This comprehensive guide covers all authentication testing scenarios using cURL with proper cookie handling, error cases, and security considerations.

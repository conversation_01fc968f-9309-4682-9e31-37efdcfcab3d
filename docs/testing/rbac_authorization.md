# RBAC Authorization Testing Guide

**Title:** Role-Based Access Control Authorization Testing Guide  
**Description:** Testing documentation for role-based access control systems, covering permission verification, role inheritance, resource access control, and authorization policy enforcement.

## Prerequisites

> **Authentication Required:** See [Authentication Guide](authentication.md) for obtaining access tokens with different roles.
> **Environment Setup:** See [Environment Configuration Guide](_env.md) for setting up testing variables.

```bash
# Load environment variables from _env.md
source <(grep '^export' docs/testing/_env.md | sed 's/^export /export /')

# Ensure you have tokens for different roles
# USER_JWT_TOKEN - Regular user token
# ADMIN_JWT_TOKEN - Administrator token
# Set these after authenticating users with different roles
```

## Role and Permission Structure

### Available Roles
- `USER` - Regular user with basic permissions
- `MODERATOR` - Enhanced user with moderation capabilities
- `ADMIN` - Full administrative access

### Resource Types
- `USER` - User management operations
- `PROFILE` - User profile operations
- `ANALY<PERSON>CS` - Analytics and reporting

### Action Types
- `CREATE` - Create new resources
- `READ` - View/retrieve resources
- `UPDATE` - Modify existing resources
- `DELETE` - Remove resources

## Authentication and Role Setup

### 1. Create Users with Different Roles

```bash
# Create regular user
echo "=== Creating Regular User ==="
curl -X POST $AUTH_BASE_URL/register \
  -H "Content-Type: $CONTENT_TYPE" \
  -H "User-Agent: $USER_AGENT" \
  -c $COOKIE_JAR \
  -d '{
    "email": "<EMAIL>",
    "password": "UserPassword123!",
    "role": "USER"
  }'

# Create admin user
echo "\n=== Creating Admin User ==="
curl -X POST $AUTH_BASE_URL/register \
  -H "Content-Type: $CONTENT_TYPE" \
  -H "User-Agent: $USER_AGENT" \
  -c $ADMIN_COOKIE_JAR \
  -d '{
    "email": "<EMAIL>",
    "password": "AdminPassword123!",
    "role": "ADMIN"
  }'

# Create moderator user
echo "\n=== Creating Moderator User ==="
MOD_COOKIE_JAR="~/moderator_cookies.txt"
touch $MOD_COOKIE_JAR
curl -X POST $AUTH_BASE_URL/register \
  -H "Content-Type: $CONTENT_TYPE" \
  -H "User-Agent: $USER_AGENT" \
  -c $MOD_COOKIE_JAR \
  -d '{
    "email": "<EMAIL>",
    "password": "ModPassword123!",
    "role": "MODERATOR"
  }'
```

### 2. Login Users and Set Environment Variables

```bash
# Function to extract cookies and set tokens
set_user_tokens() {
  echo "=== Setting up authentication tokens ==="
  
  # Login regular user
  echo "Logging in regular user..."
  curl -X POST $AUTH_BASE_URL/login \
    -H "Content-Type: $CONTENT_TYPE" \
    -c $COOKIE_JAR \
    -d '{
      "email": "<EMAIL>",
      "password": "UserPassword123!"
    }' > /dev/null
  
  # Login admin user
  echo "Logging in admin user..."
  curl -X POST $AUTH_BASE_URL/login \
    -H "Content-Type: $CONTENT_TYPE" \
    -c $ADMIN_COOKIE_JAR \
    -d '{
      "email": "<EMAIL>",
      "password": "AdminPassword123!"
    }' > /dev/null
  
  # Login moderator user
  echo "Logging in moderator user..."
  curl -X POST $AUTH_BASE_URL/login \
    -H "Content-Type: $CONTENT_TYPE" \
    -c $MOD_COOKIE_JAR \
    -d '{
      "email": "<EMAIL>",
      "password": "ModPassword123!"
    }' > /dev/null
  
  echo "All users authenticated successfully"
}

# Execute the function
set_user_tokens
```

## Permission Testing

### 1. User Permission Introspection

```bash
# Get user's own permissions
echo "=== User Permissions Introspection ==="

# Regular user permissions
echo "Regular User Permissions:"
curl -X GET $AUTH_BASE_URL/permissions \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $COOKIE_JAR | jq .

echo "\nAdmin User Permissions:"
curl -X GET $AUTH_BASE_URL/permissions \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $ADMIN_COOKIE_JAR | jq .

echo "\nModerator User Permissions:"
curl -X GET $AUTH_BASE_URL/permissions \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $MOD_COOKIE_JAR | jq .
```

**Expected Response Structure:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "clm2xyz123abc",
      "email": "<EMAIL>",
      "role": "USER"
    },
    "accessibleResources": ["PROFILE"],
    "permissions": {
      "PROFILE": ["READ", "UPDATE"]
    }
  }
}
```

### 2. Specific Permission Checks

```bash
# Check specific permissions for different users
echo "=== Specific Permission Checks ==="

# Test USER role permissions
echo "USER role - Profile READ permission:"
curl -X GET $AUTH_BASE_URL/check-permission/PROFILE/READ \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $COOKIE_JAR | jq .

echo "\nUSER role - User READ permission (should be denied):"
curl -X GET $AUTH_BASE_URL/check-permission/USER/READ \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $COOKIE_JAR | jq .

# Test ADMIN role permissions
echo "\nADMIN role - User READ permission:"
curl -X GET $AUTH_BASE_URL/check-permission/USER/READ \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $ADMIN_COOKIE_JAR | jq .

echo "\nADMIN role - Analytics READ permission:"
curl -X GET $AUTH_BASE_URL/check-permission/ANALYTICS/READ \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $ADMIN_COOKIE_JAR | jq .
```

**Expected Permission Check Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "clm2xyz123abc",
      "email": "<EMAIL>",
      "role": "USER"
    },
    "resource": "PROFILE",
    "action": "READ",
    "result": {
      "allowed": true,
      "reason": "User has permission"
    }
  }
}
```

## Resource Access Testing

### 1. Profile Resource Access

```bash
# Test profile access with different roles
echo "=== Profile Resource Access Testing ==="

# USER can access their own profile
echo "User accessing own profile:"
curl -X GET $AUTH_BASE_URL/profile \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $COOKIE_JAR | jq .

# USER can update their own profile
echo "\nUser updating own profile:"
curl -X PATCH $AUTH_BASE_URL/profile \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $COOKIE_JAR \
  -d '{
    "email": "<EMAIL>"
  }' | jq .

# ADMIN can access profile endpoints
echo "\nAdmin accessing profile:"
curl -X GET $AUTH_BASE_URL/profile \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $ADMIN_COOKIE_JAR | jq .
```

### 2. User Management Resource Access

```bash
# Test user management access (Admin only)
echo "=== User Management Resource Access Testing ==="

# USER attempting to access user list (should be denied)
echo "Regular user attempting to list users (should fail):"
curl -X GET $AUTH_BASE_URL/users \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $COOKIE_JAR

# ADMIN accessing user list (should succeed)
echo "\nAdmin accessing user list:"
curl -X GET $AUTH_BASE_URL/users \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $ADMIN_COOKIE_JAR | jq .

# Alternative RBAC endpoint test
echo "\nAdmin accessing user list via RBAC endpoint:"
curl -X GET $AUTH_BASE_URL/users-rbac \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $ADMIN_COOKIE_JAR | jq .
```

### 3. Resource Access Control Examples

```bash
# Test resource access control middleware
echo "=== Resource Access Control Middleware ==="

# Check if user can access user management resource
echo "User checking access to user management:"
curl -X GET $AUTH_BASE_URL/can-access-users \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $COOKIE_JAR

echo "\nAdmin checking access to user management:"
curl -X GET $AUTH_BASE_URL/can-access-users \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $ADMIN_COOKIE_JAR | jq .

# Check analytics access
echo "\nUser checking access to analytics:"
curl -X GET $AUTH_BASE_URL/can-access-analytics \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $COOKIE_JAR

echo "\nAdmin checking access to analytics:"
curl -X GET $AUTH_BASE_URL/can-access-analytics \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $ADMIN_COOKIE_JAR | jq .
```

## Role-Based Operations Testing

### 1. User Role Management (Admin Only)

```bash
# Test role management operations
echo "=== Role Management Testing ==="

# Get user ID for role change (from user list)
USER_ID=$(curl -s $AUTH_BASE_URL/users -b $ADMIN_COOKIE_JAR | jq -r '.data.users[0].id' 2>/dev/null || echo "user_id_here")

# Admin updating user role
echo "Admin updating user role to MODERATOR:"
curl -X PATCH $AUTH_BASE_URL/users/$USER_ID/role \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $ADMIN_COOKIE_JAR \
  -d '{
    "role": "MODERATOR"
  }' | jq .

# Regular user attempting to update roles (should fail)
echo "\nRegular user attempting to update role (should fail):"
curl -X PATCH $AUTH_BASE_URL/users/$USER_ID/role \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $COOKIE_JAR \
  -d '{
    "role": "ADMIN"
  }'
```

### 2. User Deletion (Admin Only)

```bash
# Test user deletion permissions
echo "=== User Deletion Testing ==="

# Create a test user to delete
echo "Creating test user for deletion:"
TEST_DELETE_USER=$(curl -s -X POST $AUTH_BASE_URL/register \
  -H "Content-Type: $CONTENT_TYPE" \
  -d '{
    "email": "<EMAIL>",
    "password": "DeleteTest123!"
  }' | jq -r '.data.user.id' 2>/dev/null || echo "test_user_id")

# Regular user attempting to delete (should fail)
echo "\nRegular user attempting to delete user (should fail):"
curl -X DELETE $AUTH_BASE_URL/users/$TEST_DELETE_USER \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $COOKIE_JAR

# Admin deleting user (should succeed)
echo "\nAdmin deleting user:"
curl -X DELETE $AUTH_BASE_URL/users/$TEST_DELETE_USER \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $ADMIN_COOKIE_JAR | jq .
```

## Edge Cases and Security Testing

### 1. Role Escalation Attempts

```bash
# Test for potential role escalation vulnerabilities
echo "=== Role Escalation Testing ==="

# Attempt to escalate own role
echo "User attempting to escalate own role:"
curl -X PATCH $AUTH_BASE_URL/profile \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $COOKIE_JAR \
  -d '{
    "role": "ADMIN"
  }'

# Attempt to manipulate role in requests
echo "\nUser attempting role manipulation in headers:"
curl -X GET $AUTH_BASE_URL/users \
  -H "Content-Type: $CONTENT_TYPE" \
  -H "X-User-Role: ADMIN" \
  -b $COOKIE_JAR

# Attempt to access admin endpoints with modified tokens
echo "\nTesting with potentially modified authorization:"
curl -X GET $AUTH_BASE_URL/users \
  -H "Content-Type: $CONTENT_TYPE" \
  -H "Authorization: Bearer fake.admin.token" \
  -b $COOKIE_JAR
```

### 2. Resource Boundary Testing

```bash
# Test resource access boundaries
echo "=== Resource Boundary Testing ==="

# Attempt cross-user resource access
echo "Testing cross-user profile access:"
# Get another user's ID
OTHER_USER_ID=$(curl -s $AUTH_BASE_URL/users -b $ADMIN_COOKIE_JAR | jq -r '.data.users[1].id' 2>/dev/null || echo "other_user_id")

# Regular user trying to access another user's data
curl -X GET $USERS_BASE_URL/$OTHER_USER_ID \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $COOKIE_JAR

# Admin should be able to access any user's data
echo "\nAdmin accessing other user data:"
curl -X GET $USERS_BASE_URL/$OTHER_USER_ID \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $ADMIN_COOKIE_JAR | jq .
```

## Comprehensive RBAC Test Suite

```bash
#!/bin/bash
# rbac_comprehensive_test.sh

set -e

# Load environment variables
source <(grep '^export' docs/testing/_env.md | sed 's/^export /export /' 2>/dev/null || echo "")

echo "🔐 Comprehensive RBAC Authorization Test Suite"
echo "============================================"

# Test results tracking
tests_passed=0
tests_failed=0
tests_total=0

# Test function
run_rbac_test() {
  local test_name="$1"
  local expected_status="$2"
  local curl_command="$3"
  
  tests_total=$((tests_total + 1))
  echo "\n🧪 Test: $test_name"
  
  # Execute test
  actual_status=$(eval "$curl_command" 2>/dev/null | tail -c 4 | head -c 3 || echo "000")
  
  if [[ "$actual_status" == "$expected_status" ]]; then
    echo "✅ PASS - Expected: $expected_status, Got: $actual_status"
    tests_passed=$((tests_passed + 1))
  else
    echo "❌ FAIL - Expected: $expected_status, Got: $actual_status"
    tests_failed=$((tests_failed + 1))
  fi
}

# Authentication tests
echo "\n=== Authentication Setup ==="
# (Setup code here - creating users and logging in)

# Permission tests
echo "\n=== Permission Tests ==="

run_rbac_test "User can access own profile" "200" \
  "curl -s -w '%{http_code}' -o /dev/null $AUTH_BASE_URL/profile -b $COOKIE_JAR"

run_rbac_test "User cannot access user list" "403" \
  "curl -s -w '%{http_code}' -o /dev/null $AUTH_BASE_URL/users -b $COOKIE_JAR"

run_rbac_test "Admin can access user list" "200" \
  "curl -s -w '%{http_code}' -o /dev/null $AUTH_BASE_URL/users -b $ADMIN_COOKIE_JAR"

run_rbac_test "User cannot access analytics" "403" \
  "curl -s -w '%{http_code}' -o /dev/null $AUTH_BASE_URL/can-access-analytics -b $COOKIE_JAR"

run_rbac_test "Admin can access analytics" "200" \
  "curl -s -w '%{http_code}' -o /dev/null $AUTH_BASE_URL/can-access-analytics -b $ADMIN_COOKIE_JAR"

# Role management tests
echo "\n=== Role Management Tests ==="

run_rbac_test "User cannot change roles" "403" \
  "curl -s -w '%{http_code}' -o /dev/null -X PATCH $AUTH_BASE_URL/users/test_id/role -b $COOKIE_JAR -d '{"role":"ADMIN"}' -H 'Content-Type: application/json'"

run_rbac_test "Admin can change roles" "200" \
  "curl -s -w '%{http_code}' -o /dev/null -X PATCH $AUTH_BASE_URL/users/test_id/role -b $ADMIN_COOKIE_JAR -d '{"role":"MODERATOR"}' -H 'Content-Type: application/json'"

# Summary
echo "\n=== Test Results Summary ==="
echo "Total Tests: $tests_total"
echo "Passed: $tests_passed"
echo "Failed: $tests_failed"
echo "Success Rate: $(echo "scale=1; $tests_passed * 100 / $tests_total" | bc -l 2>/dev/null || echo "N/A")%"

if [[ $tests_failed -eq 0 ]]; then
  echo "\n🎉 All RBAC tests passed!"
  exit 0
else
  echo "\n⚠️ Some RBAC tests failed. Review the output above."
  exit 1
fi
```

## Expected Response Formats

### Permission Check Response
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "role": "USER"
    },
    "resource": "PROFILE",
    "action": "UPDATE",
    "result": {
      "allowed": true,
      "reason": "User has permission to update their profile"
    }
  }
}
```

### Access Denied Response
```json
{
  "success": false,
  "message": "Insufficient permissions",
  "error": {
    "code": "INSUFFICIENT_PERMISSIONS",
    "requiredPermission": "USER:READ",
    "userRole": "USER"
  },
  "statusCode": 403,
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Resource Access Response
```json
{
  "success": true,
  "message": "User has access to user management",
  "data": {
    "resource": "USER",
    "user": {
      "id": "admin_user_id",
      "role": "ADMIN"
    }
  }
}
```

This comprehensive RBAC testing guide ensures that your role-based access control system properly enforces permissions, prevents privilege escalation, and maintains secure resource boundaries across all user roles.

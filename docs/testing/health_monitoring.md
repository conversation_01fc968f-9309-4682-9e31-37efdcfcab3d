# Health Monitoring Testing Guide

**Title:** Health Monitoring Testing Guide  
**Description:** Testing documentation for system health monitoring, including health check endpoints, metrics collection, alerting mechanisms, performance monitoring, and uptime verification.

## Prerequisites

> **Environment Setup:** See [Environment Configuration Guide](_env.md) for setting up testing variables.

```bash
# Load environment variables from _env.md
source <(grep '^export' docs/testing/_env.md | sed 's/^export /export /')
```

## Health Check Endpoints

### 1. Basic Health Check

```bash
# Test basic server health
curl -X GET $HEALTH_URL \
  -H "Content-Type: $CONTENT_TYPE" \
  -H "User-Agent: $USER_AGENT"
```

**Expected Response (200 OK):**
```json
{
  "status": "OK",
  "message": "Secure Backend Server is running",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "uptime": 3600.123,
  "features": {
    "authentication": "JWT-based",
    "authorization": "RBAC with granular permissions",
    "validation": "class-validator with DTOs",
    "rateLimit": "express-rate-limit",
    "logging": "Winston with morgan",
    "swagger": "Available at /api-docs"
  }
}
```

### 2. API Root Health Check

```bash
# Test API root endpoint
curl -X GET $BASE_URL \
  -H "Content-Type: $CONTENT_TYPE" \
  -H "User-Agent: $USER_AGENT"
```

**Expected Response (200 OK):**
```json
{
  "message": "Secure Backend API with RBAC",
  "version": "1.0.0",
  "features": [
    "JWT Authentication",
    "Role-Based Access Control (RBAC)",
    "Permission-based Authorization",
    "Request Rate Limiting",
    "Data Validation",
    "Comprehensive Logging"
  ]
}
```

## Performance Monitoring

### 1. Response Time Testing

```bash
# Test response times for critical endpoints
echo "=== Response Time Analysis ==="

# Health check response time
echo "Health Check:"
curl -w "Response Time: %{time_total}s\n" -s -o /dev/null $HEALTH_URL

# API root response time
echo "API Root:"
curl -w "Response Time: %{time_total}s\n" -s -o /dev/null $BASE_URL

# Authentication endpoint response time
echo "Auth Login (no auth):"
curl -X POST $AUTH_BASE_URL/login \
  -H "Content-Type: $CONTENT_TYPE" \
  -d '{"email":"<EMAIL>","password":"invalid"}' \
  -w "Response Time: %{time_total}s\n" \
  -s -o /dev/null
```

### 2. Load Testing

```bash
# Simple load test using ab (Apache Bench)
if command -v ab > /dev/null; then
  echo "=== Load Testing with Apache Bench ==="
  
  # Test health endpoint under load
  echo "Health endpoint - 100 requests, 10 concurrent:"
  ab -n 100 -c 10 $HEALTH_URL
  
  # Test API root under load
  echo "API root - 50 requests, 5 concurrent:"
  ab -n 50 -c 5 $BASE_URL
else
  echo "Apache Bench not available, using curl for basic load test"
  
  echo "Testing 20 concurrent requests to health endpoint:"
  for i in {1..20}; do
    curl -s -w "Request $i: %{http_code} - %{time_total}s\n" -o /dev/null $HEALTH_URL &
  done
  wait
fi
```

### 3. Memory and Resource Monitoring

```bash
# Monitor server resources during testing
echo "=== Resource Monitoring ==="

# Function to check server resources
check_resources() {
  local test_name=$1
  echo "--- $test_name ---"
  
  # Check process information if running locally
  if pgrep -f "node.*server" > /dev/null; then
    local pid=$(pgrep -f "node.*server")
    echo "Process ID: $pid"
    
    # Memory usage
    if command -v ps > /dev/null; then
      ps -p $pid -o pid,pcpu,pmem,rss,vsz
    fi
    
    # File descriptors
    if [[ -d "/proc/$pid/fd" ]]; then
      echo "Open file descriptors: $(ls /proc/$pid/fd | wc -l)"
    fi
  else
    echo "Server process not found locally"
  fi
  
  # Test endpoint responsiveness
  local start_time=$(date +%s.%N)
  local response=$(curl -s -w "%{http_code}" -o /dev/null $HEALTH_URL)
  local end_time=$(date +%s.%N)
  local duration=$(echo "$end_time - $start_time" | bc -l 2>/dev/null || echo "N/A")
  
  echo "Health check: HTTP $response in ${duration}s"
  echo ""
}

# Check resources before, during, and after load
check_resources "Before Load Test"

# Generate some load in background
for i in {1..10}; do
  curl -s $HEALTH_URL > /dev/null &
  curl -s $BASE_URL > /dev/null &
done

check_resources "During Load Test"

# Wait for background processes to complete
wait

check_resources "After Load Test"
```

## Uptime and Availability Testing

### 1. Continuous Health Monitoring

```bash
# Monitor health over time
echo "=== Continuous Health Monitoring ==="
echo "Monitoring health for 60 seconds (every 5 seconds)..."

successful_checks=0
failed_checks=0
total_checks=0

for i in {1..12}; do
  total_checks=$((total_checks + 1))
  timestamp=$(date '+%Y-%m-%d %H:%M:%S')
  
  response=$(curl -s -w "%{http_code}" -o /dev/null $HEALTH_URL 2>/dev/null)
  
  if [[ "$response" == "200" ]]; then
    echo "[$timestamp] ✅ Health Check: OK (HTTP $response)"
    successful_checks=$((successful_checks + 1))
  else
    echo "[$timestamp] ❌ Health Check: FAILED (HTTP $response)"
    failed_checks=$((failed_checks + 1))
  fi
  
  sleep 5
done

echo ""
echo "=== Health Monitoring Summary ==="
echo "Total Checks: $total_checks"
echo "Successful: $successful_checks"
echo "Failed: $failed_checks"
echo "Success Rate: $(echo "scale=2; $successful_checks * 100 / $total_checks" | bc -l 2>/dev/null || echo "N/A")%"
```

### 2. Service Dependency Checks

```bash
# Check if required services are accessible
echo "=== Service Dependency Checks ==="

# Test database connectivity (indirect via API)
echo "Testing Database Connectivity:"
db_test_response=$(curl -s -X POST $AUTH_BASE_URL/login \
  -H "Content-Type: $CONTENT_TYPE" \
  -d '{"email":"<EMAIL>","password":"test"}' \
  -w "%{http_code}")

if [[ "${db_test_response: -3}" == "401" ]]; then
  echo "✅ Database: Responding (authentication rejected as expected)"
elif [[ "${db_test_response: -3}" == "400" ]]; then
  echo "✅ Database: Responding (validation error as expected)"
else
  echo "⚠️ Database: Unexpected response (${db_test_response: -3})"
fi

# Test external service connectivity (if applicable)
echo "Testing External Services:"
# Add any external service checks here
echo "✅ No external services configured"
```

## Error Condition Testing

### 1. Server Overload Simulation

```bash
# Test server behavior under high load
echo "=== Server Overload Testing ==="

echo "Generating high load (50 concurrent requests)..."
for i in {1..50}; do
  {
    response=$(curl -s -w "%{http_code}" -o /dev/null $HEALTH_URL 2>/dev/null)
    echo "Request $i: HTTP $response"
  } &
done

# Wait for all requests to complete
wait

echo "High load test completed"

# Check if server is still responsive
echo "Post-load health check:"
post_load_response=$(curl -s -w "HTTP: %{http_code}, Time: %{time_total}s\n" -o /dev/null $HEALTH_URL)
echo "$post_load_response"
```

### 2. Invalid Request Handling

```bash
# Test how server handles malformed requests
echo "=== Invalid Request Handling ==="

# Test with invalid HTTP methods
echo "Testing invalid HTTP method:"
curl -X INVALID $HEALTH_URL \
  -w "HTTP: %{http_code}\n" \
  -s -o /dev/null

# Test with extremely long URLs
echo "Testing long URL:"
long_path="/$(printf 'a%.0s' {1..1000})"
curl -X GET "http://localhost:3000$long_path" \
  -w "HTTP: %{http_code}\n" \
  -s -o /dev/null

# Test with invalid headers
echo "Testing invalid headers:"
curl -X GET $HEALTH_URL \
  -H "Invalid-Header: $(printf 'x%.0s' {1..10000})" \
  -w "HTTP: %{http_code}\n" \
  -s -o /dev/null
```

## Metrics Collection

### 1. Response Time Metrics

```bash
# Collect detailed response time metrics
echo "=== Response Time Metrics Collection ==="

metrics_file="health_metrics_$(date +%Y%m%d_%H%M%S).csv"
echo "timestamp,endpoint,http_code,time_total,time_connect,time_appconnect" > $metrics_file

endpoints=(
  "$HEALTH_URL:health"
  "$BASE_URL:api-root"
  "$AUTH_BASE_URL/profile:auth-profile"
)

echo "Collecting metrics for 30 seconds..."
end_time=$((SECONDS + 30))

while [ $SECONDS -lt $end_time ]; do
  for endpoint_info in "${endpoints[@]}"; do
    IFS=':' read -r url name <<< "$endpoint_info"
    
    # Collect metrics
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    metrics=$(curl -s -w "%{http_code},%{time_total},%{time_connect},%{time_appconnect}" -o /dev/null "$url" 2>/dev/null)
    
    echo "$timestamp,$name,$metrics" >> $metrics_file
    
    sleep 1
  done
done

echo "Metrics collected in: $metrics_file"

# Basic analysis
echo ""
echo "=== Basic Metrics Analysis ==="
if command -v awk > /dev/null; then
  echo "Average response times:"
  tail -n +2 $metrics_file | awk -F',' '
    { 
      endpoint[$2] += $4
      count[$2]++
    } 
    END { 
      for (e in endpoint) 
        printf "%s: %.3fs\n", e, endpoint[e]/count[e] 
    }'
else
  echo "AWK not available for metrics analysis"
  echo "Raw metrics saved to: $metrics_file"
fi
```

### 2. Error Rate Monitoring

```bash
# Monitor error rates across endpoints
echo "=== Error Rate Monitoring ==="

success_count=0
error_count=0
timeout_count=0

for i in {1..20}; do
  # Test health endpoint
  response=$(timeout 10s curl -s -w "%{http_code}" -o /dev/null $HEALTH_URL 2>/dev/null)
  exit_code=$?
  
  if [[ $exit_code -eq 124 ]]; then
    echo "Request $i: TIMEOUT"
    timeout_count=$((timeout_count + 1))
  elif [[ "$response" =~ ^[23][0-9][0-9]$ ]]; then
    echo "Request $i: SUCCESS (HTTP $response)"
    success_count=$((success_count + 1))
  else
    echo "Request $i: ERROR (HTTP $response)"
    error_count=$((error_count + 1))
  fi
  
  sleep 0.5
done

echo ""
echo "=== Error Rate Summary ==="
echo "Successful Requests: $success_count"
echo "Error Requests: $error_count"
echo "Timeout Requests: $timeout_count"
total_requests=$((success_count + error_count + timeout_count))
if [[ $total_requests -gt 0 ]]; then
  success_rate=$(echo "scale=2; $success_count * 100 / $total_requests" | bc -l 2>/dev/null || echo "N/A")
  echo "Success Rate: ${success_rate}%"
fi
```

## Health Monitoring Scripts

### 1. Complete Health Check Script

```bash
#!/bin/bash
# health_check_comprehensive.sh

set -e

# Load environment variables
source <(grep '^export' docs/testing/_env.md | sed 's/^export /export /' 2>/dev/null || echo "")

# Default values if env vars not set
BASE_URL="${BASE_URL:-http://localhost:3000/api/v1}"
HEALTH_URL="${HEALTH_URL:-http://localhost:3000/health}"
AUTH_BASE_URL="${AUTH_BASE_URL:-http://localhost:3000/api/v1/auth}"

echo "🏥 Comprehensive Health Check Suite"
echo "Testing against: $HEALTH_URL"
echo "======================================="

# Test 1: Basic connectivity
echo "\n📡 Test 1: Basic Connectivity"
if timeout 10s curl -s $HEALTH_URL > /dev/null 2>&1; then
  echo "✅ Server is reachable"
else
  echo "❌ Server is not reachable"
  exit 1
fi

# Test 2: Health endpoint response
echo "\n🔍 Test 2: Health Endpoint Response"
health_response=$(curl -s $HEALTH_URL 2>/dev/null || echo "{}")
if echo "$health_response" | grep -q '"status".*"OK"'; then
  echo "✅ Health endpoint returns OK status"
else
  echo "⚠️ Health endpoint response may be invalid"
  echo "Response: $health_response"
fi

# Test 3: API root accessibility
echo "\n🌐 Test 3: API Root Accessibility"
api_response=$(curl -s -w "%{http_code}" -o /dev/null $BASE_URL 2>/dev/null || echo "000")
if [[ "$api_response" == "200" ]]; then
  echo "✅ API root is accessible"
else
  echo "⚠️ API root returned HTTP $api_response"
fi

# Test 4: Response time check
echo "\n⏱️ Test 4: Response Time Check"
response_time=$(curl -s -w "%{time_total}" -o /dev/null $HEALTH_URL 2>/dev/null || echo "0")
if command -v bc > /dev/null && [[ $(echo "$response_time < 2.0" | bc 2>/dev/null) -eq 1 ]]; then
  echo "✅ Response time acceptable: ${response_time}s"
else
  echo "⚠️ Response time may be slow: ${response_time}s"
fi

# Test 5: Authentication system check
echo "\n🔐 Test 5: Authentication System Check"
auth_response=$(curl -s -w "%{http_code}" -o /dev/null $AUTH_BASE_URL/profile 2>/dev/null || echo "000")
if [[ "$auth_response" == "401" ]]; then
  echo "✅ Authentication system is responding correctly"
else
  echo "⚠️ Authentication system returned unexpected HTTP $auth_response"
fi

echo "\n🏥 Health check completed!"
echo "Check any warnings above for potential issues."
```

### 2. Monitoring Dashboard Script

```bash
#!/bin/bash
# health_dashboard.sh - Real-time health monitoring

# Clear screen function
clear_screen() {
  printf "\033c"
}

# Display health status
show_health_status() {
  local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
  
  echo "==========================================="
  echo "      🏥 HEALTH MONITORING DASHBOARD"
  echo "      Last Updated: $timestamp"
  echo "==========================================="
  echo ""
  
  # Health check
  local health_status="❌ DOWN"
  local health_time="N/A"
  
  if health_response=$(timeout 5s curl -s -w "%{time_total}" $HEALTH_URL 2>/dev/null); then
    if echo "$health_response" | grep -q '"status".*"OK"'; then
      health_status="✅ UP"
      health_time=$(echo "$health_response" | tail -1)
    fi
  fi
  
  echo "🔍 HEALTH ENDPOINT:    $health_status ($health_time s)"
  
  # API status
  local api_status="❌ DOWN"
  local api_code=$(timeout 5s curl -s -w "%{http_code}" -o /dev/null $BASE_URL 2>/dev/null || echo "000")
  
  if [[ "$api_code" == "200" ]]; then
    api_status="✅ UP"
  fi
  
  echo "🌐 API ENDPOINT:       $api_status (HTTP $api_code)"
  
  # Authentication status
  local auth_status="❌ DOWN"
  local auth_code=$(timeout 5s curl -s -w "%{http_code}" -o /dev/null $AUTH_BASE_URL/profile 2>/dev/null || echo "000")
  
  if [[ "$auth_code" == "401" ]]; then
    auth_status="✅ UP"
  fi
  
  echo "🔐 AUTH ENDPOINT:      $auth_status (HTTP $auth_code)"
  
  echo ""
  echo "Press Ctrl+C to stop monitoring..."
}

# Main monitoring loop
echo "Starting health monitoring dashboard..."
echo "Press Ctrl+C to stop"
sleep 2

while true; do
  clear_screen
  show_health_status
  sleep 10
done
```

These comprehensive health monitoring tests ensure your secure backend API maintains optimal performance, availability, and reliability across different load conditions and scenarios.

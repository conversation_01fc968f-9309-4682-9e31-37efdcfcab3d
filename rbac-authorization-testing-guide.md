# RBAC & Authorization Testing Guide

## Table of Contents
1. [Role Matrix & Permission Hierarchy](#role-matrix--permission-hierarchy)
2. [Basic Authorization Testing](#basic-authorization-testing)
3. [Token Attachment Methods](#token-attachment-methods)
4. [Negative Testing Scenarios](#negative-testing-scenarios)
5. [Dynamic Role Testing Workflow](#dynamic-role-testing-workflow)
6. [Advanced Testing Patterns](#advanced-testing-patterns)

## Role Matrix & Permission Hierarchy

### Understanding RBAC Structure

Role-Based Access Control (RBAC) follows a hierarchical structure where:
- **Users** are assigned to **Roles**
- **Roles** contain **Permissions**
- **Permissions** grant access to **Resources** and **Actions**

### Sample Role Matrix

```
┌─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐
│   Resource  │   Guest     │   User      │   Manager   │   Admin     │
├─────────────┼─────────────┼─────────────┼─────────────┼─────────────┤
│ Public API  │ READ        │ READ        │ READ        │ READ/WRITE  │
│ User Data   │ -           │ READ/WRITE* │ READ        │ READ/WRITE  │
│ Reports     │ -           │ -           │ READ/WRITE  │ READ/WRITE  │
│ Admin Panel │ -           │ -           │ -           │ READ/WRITE  │
│ User Mgmt   │ -           │ -           │ READ        │ READ/WRITE  │
│ System Config│ -          │ -           │ -           │ READ/WRITE  │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘
* Own data only
```

### Permission Hierarchy

```
Admin (Superuser)
├── All permissions
└── Can grant/revoke roles

Manager
├── User management (read)
├── Reports (read/write)
├── User data (read all)
└── Cannot modify system config

User
├── Own profile (read/write)
├── Public resources (read)
└── Cannot access admin features

Guest
└── Public resources (read-only)
```

## Basic Authorization Testing

### Test Environment Setup

First, let's define our test endpoints and tokens:

```bash
# Environment variables for testing
export API_BASE_URL="https://api.example.com"
export ADMIN_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
export USER_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
export GUEST_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### Core Authorization Tests

#### Test 1: Regular User Accessing Admin Route (Expect 403)

```bash
# Test: User token accessing admin-only endpoint
curl -X GET \
  "${API_BASE_URL}/admin/users" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -v

# Expected Response:
# HTTP Status: 403
# {
#   "error": "Forbidden",
#   "message": "Insufficient permissions to access this resource",
#   "required_role": "admin"
# }
```

#### Test 2: Admin Token Accessing Same Route (Expect 200)

```bash
# Test: Admin token accessing admin endpoint
curl -X GET \
  "${API_BASE_URL}/admin/users" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -v

# Expected Response:
# HTTP Status: 200
# {
#   "users": [
#     {"id": 1, "username": "john_doe", "role": "user"},
#     {"id": 2, "username": "admin_user", "role": "admin"}
#   ]
# }
```

#### Test 3: Guest Accessing User Route (Expect 403)

```bash
# Test: Guest token accessing user-only endpoint
curl -X GET \
  "${API_BASE_URL}/user/profile" \
  -H "Authorization: Bearer ${GUEST_TOKEN}" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n"

# Expected Response:
# HTTP Status: 403
```

## Token Attachment Methods

### 1. Bearer Token Authentication

```bash
# Standard Bearer token in Authorization header
curl -X GET \
  "${API_BASE_URL}/protected-endpoint" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json"
```

### 2. Cookie-Based Authentication

```bash
# Using cookies for session-based auth
curl -X GET \
  "${API_BASE_URL}/protected-endpoint" \
  -H "Cookie: session_id=abc123xyz; auth_token=def456uvw" \
  -H "Content-Type: application/json"

# Or using cookie jar
curl -X GET \
  "${API_BASE_URL}/protected-endpoint" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

### 3. Custom Header Authentication

```bash
# Custom authentication header
curl -X GET \
  "${API_BASE_URL}/protected-endpoint" \
  -H "X-API-Key: your-api-key-here" \
  -H "X-User-Token: ${USER_TOKEN}" \
  -H "Content-Type: application/json"
```

### 4. Query Parameter Authentication (Less Secure)

```bash
# Token as query parameter (use cautiously)
curl -X GET \
  "${API_BASE_URL}/protected-endpoint?token=${TOKEN}" \
  -H "Content-Type: application/json"
```

## Negative Testing Scenarios

### 1. Testing with Disabled Role

```bash
# Simulate disabled user account
export DISABLED_USER_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.disabled_user_token"

curl -X GET \
  "${API_BASE_URL}/user/dashboard" \
  -H "Authorization: Bearer ${DISABLED_USER_TOKEN}" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n"

# Expected Response:
# HTTP Status: 403
# {
#   "error": "Account Disabled",
#   "message": "Your account has been disabled. Contact administrator."
# }
```

### 2. Testing with Expired Token

```bash
# Simulate expired token
export EXPIRED_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.expired_token"

curl -X GET \
  "${API_BASE_URL}/user/profile" \
  -H "Authorization: Bearer ${EXPIRED_TOKEN}" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n"

# Expected Response:
# HTTP Status: 401
# {
#   "error": "Token Expired",
#   "message": "Authentication token has expired. Please log in again."
# }
```

### 3. Testing with Invalid/Malformed Token

```bash
# Invalid token format
curl -X GET \
  "${API_BASE_URL}/user/profile" \
  -H "Authorization: Bearer invalid_token_format" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n"

# Expected Response:
# HTTP Status: 401
# {
#   "error": "Invalid Token",
#   "message": "Authentication token is invalid or malformed."
# }
```

### 4. Testing with No Authentication

```bash
# No authentication header
curl -X GET \
  "${API_BASE_URL}/admin/users" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n"

# Expected Response:
# HTTP Status: 401
# {
#   "error": "Authentication Required",
#   "message": "This endpoint requires authentication."
# }
```

## Dynamic Role Testing Workflow

### Step 1: Initial State Verification

```bash
# Get current user role
curl -X GET \
  "${API_BASE_URL}/user/me" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  -H "Content-Type: application/json" | jq '.role'

# Test current permissions
curl -X GET \
  "${API_BASE_URL}/admin/users" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n"
```

### Step 2: Grant Role/Permission

```bash
# Admin grants manager role to user
curl -X POST \
  "${API_BASE_URL}/admin/users/123/roles" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "role": "manager",
    "permissions": ["user:read", "reports:write"]
  }' \
  -w "\nStatus: %{http_code}\n"

# Expected Response:
# Status: 200
# {
#   "message": "Role granted successfully",
#   "user_id": 123,
#   "new_role": "manager"
# }
```

### Step 3: Re-test with New Token

```bash
# User needs to get new token with updated permissions
# (In real scenario, user would re-authenticate)
export UPDATED_USER_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.updated_token"

# Test previously forbidden endpoint
curl -X GET \
  "${API_BASE_URL}/reports/monthly" \
  -H "Authorization: Bearer ${UPDATED_USER_TOKEN}" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n"

# Expected Response:
# Status: 200 (now accessible)
```

### Step 4: Verify Role Hierarchy

```bash
# Test that manager can read user data but not admin functions
curl -X GET \
  "${API_BASE_URL}/users/456/profile" \
  -H "Authorization: Bearer ${UPDATED_USER_TOKEN}" \
  -w "\nUser Profile Access: %{http_code}\n"

curl -X GET \
  "${API_BASE_URL}/admin/system/config" \
  -H "Authorization: Bearer ${UPDATED_USER_TOKEN}" \
  -w "\nAdmin Config Access: %{http_code}\n"

# Expected:
# User Profile Access: 200 (allowed)
# Admin Config Access: 403 (still forbidden)
```

### Step 5: Role Revocation Testing

```bash
# Admin revokes manager role
curl -X DELETE \
  "${API_BASE_URL}/admin/users/123/roles/manager" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -H "Content-Type: application/json" \
  -w "\nRevocation Status: %{http_code}\n"

# Test with revoked permissions
curl -X GET \
  "${API_BASE_URL}/reports/monthly" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  -w "\nAccess After Revocation: %{http_code}\n"

# Expected: 403 (access revoked)
```

## Advanced Testing Patterns

### Resource-Specific Permission Testing

```bash
# Test user can only access their own data
USER_ID=123
OTHER_USER_ID=456

# Should succeed - own data
curl -X GET \
  "${API_BASE_URL}/users/${USER_ID}/profile" \
  -H "Authorization: Bearer ${USER_TOKEN}"

# Should fail - other user's data
curl -X GET \
  "${API_BASE_URL}/users/${OTHER_USER_ID}/profile" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  -w "\nStatus: %{http_code}\n"
```

### Batch Permission Testing Script

```bash
#!/bin/bash

# Test multiple endpoints with different roles
declare -A ENDPOINTS=(
  ["/public/status"]="200,200,200,200"           # guest,user,manager,admin
  ["/user/profile"]="401,200,200,200"            # guest,user,manager,admin
  ["/reports/monthly"]="401,401,200,200"         # guest,user,manager,admin
  ["/admin/users"]="401,403,403,200"             # guest,user,manager,admin
)

declare -A TOKENS=(
  ["guest"]="${GUEST_TOKEN}"
  ["user"]="${USER_TOKEN}"
  ["manager"]="${MANAGER_TOKEN}"
  ["admin"]="${ADMIN_TOKEN}"
)

for endpoint in "${!ENDPOINTS[@]}"; do
  echo "Testing endpoint: ${endpoint}"
  IFS=',' read -ra EXPECTED <<< "${ENDPOINTS[$endpoint]}"
  
  i=0
  for role in "guest" "user" "manager" "admin"; do
    response_code=$(curl -s -o /dev/null -w "%{http_code}" \
      "${API_BASE_URL}${endpoint}" \
      -H "Authorization: Bearer ${TOKENS[$role]}")
    
    expected="${EXPECTED[$i]}"
    if [[ "$response_code" == "$expected" ]]; then
      echo "  ✓ ${role}: ${response_code} (expected ${expected})"
    else
      echo "  ✗ ${role}: ${response_code} (expected ${expected})"
    fi
    ((i++))
  done
  echo
done
```

### Testing with Different HTTP Methods

```bash
# Test CRUD operations with different roles
RESOURCE_ID=123

# CREATE - Only admin can create users
curl -X POST \
  "${API_BASE_URL}/users" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{"username": "newuser", "email": "<EMAIL>"}' \
  -w "\nUser Create: %{http_code}\n"

curl -X POST \
  "${API_BASE_URL}/users" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{"username": "newuser", "email": "<EMAIL>"}' \
  -w "\nAdmin Create: %{http_code}\n"

# READ - Manager can read, user cannot
curl -X GET \
  "${API_BASE_URL}/users/${RESOURCE_ID}" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  -w "\nUser Read: %{http_code}\n"

curl -X GET \
  "${API_BASE_URL}/users/${RESOURCE_ID}" \
  -H "Authorization: Bearer ${MANAGER_TOKEN}" \
  -w "\nManager Read: %{http_code}\n"

# UPDATE - Different update permissions
curl -X PUT \
  "${API_BASE_URL}/users/${RESOURCE_ID}" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}' \
  -w "\nUser Update: %{http_code}\n"

# DELETE - Only admin can delete
curl -X DELETE \
  "${API_BASE_URL}/users/${RESOURCE_ID}" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -w "\nAdmin Delete: %{http_code}\n"
```

## Best Practices for RBAC Testing

1. **Always test the negative cases**: Ensure unauthorized access is properly blocked
2. **Test boundary conditions**: What happens at role transitions?
3. **Validate token expiration**: Ensure expired tokens are rejected
4. **Test with malformed requests**: Invalid tokens, missing headers, etc.
5. **Verify cascade effects**: When roles change, ensure all dependent permissions update
6. **Test with real-world scenarios**: Mixed permissions, partial access, etc.
7. **Automate repetitive tests**: Use scripts to test multiple endpoints systematically
8. **Document expected behaviors**: Clear expectations for each role/endpoint combination

## Troubleshooting Common Issues

### Token Not Working
```bash
# Verify token format and claims
echo "${TOKEN}" | base64 -d | jq '.'

# Check token expiration
curl -X GET \
  "${API_BASE_URL}/auth/verify" \
  -H "Authorization: Bearer ${TOKEN}"
```

### Unexpected 403 Errors
```bash
# Check current user permissions
curl -X GET \
  "${API_BASE_URL}/user/permissions" \
  -H "Authorization: Bearer ${TOKEN}"

# Verify role assignments
curl -X GET \
  "${API_BASE_URL}/user/roles" \
  -H "Authorization: Bearer ${TOKEN}"
```

This guide provides a comprehensive framework for testing RBAC and authorization systems using cURL commands and practical testing workflows.

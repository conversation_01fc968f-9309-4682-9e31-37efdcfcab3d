# Secure Backend API

A production-ready Express.js backend built with TypeScript, featuring JWT authentication, OAuth 2.0, Role-Based Access Control (RBAC), and a clean layered architecture. This template provides a comprehensive foundation for building secure, scalable APIs with enterprise-grade features.

## Features

*   **Authentication**: Secure JWT-based authentication with token refresh and password recovery mechanisms.
*   **OAuth 2.0 Integration**: Seamless integration with Google, Facebook, and GitHub for social logins.
*   **Authorization**: Robust Role-Based Access Control (RBAC) system with granular permissions.
*   **Email Verification**: Secure email verification process to ensure the authenticity of user accounts.
*   **Request Validation**: DTO-based request validation using `class-validator` to ensure data integrity.
*   **Rate Limiting**: Protection against brute-force attacks and API abuse.
*   **Comprehensive Logging**: Detailed logging using Winston and Morgan for effective monitoring and debugging.
*   **Error Handling**: Centralized error handling middleware to ensure consistent and predictable error responses.
*   **TypeScript**: Full type safety throughout the application for improved code quality and maintainability.
*   **Prisma ORM**: Type-safe database access and management with Prisma ORM.
*   **Security Middlewares**: Essential security middlewares like Helmet, HPP, and CORS to protect against common vulnerabilities.
*   **API Documentation**: Interactive API documentation using Swagger/OpenAPI.
*   **Testing Suite**: Comprehensive testing suite with Jest to ensure code quality and reliability.
*   **Production-Ready**: Includes support for PM2 for process management and clustering in production environments.

## Architecture

This backend follows a **Layered Architecture (N-Tier)** pattern, ensuring separation of concerns and maintainability:

### Architecture Layers

```
┌─────────────────────────────────────────────────────────┐
│                    Routes Layer                         │
│              (HTTP Request Handling)                   │
├─────────────────────────────────────────────────────────┤
│                  Controllers Layer                      │
│            (Request/Response Logic)                     │
├─────────────────────────────────────────────────────────┤
│                   Services Layer                        │
│              (Business Logic)                          │
├─────────────────────────────────────────────────────────┤
│                Data Access Layer                        │
│              (Database Operations)                      │
└─────────────────────────────────────────────────────────┘
```

#### 1. **Routes Layer** (`src/routes/`)
- **Responsibility**: API endpoint definitions and routing
- **Files**: `*.routes.ts` (e.g., `auth.routes.ts`, `user.route.ts`)
- **Function**: Maps HTTP methods and URLs to controller actions

#### 2. **Controllers Layer** (`src/controllers/`)
- **Responsibility**: Request/response handling and input validation
- **Files**: `*.controller.ts` (e.g., `auth.controller.ts`, `user.controller.ts`)
- **Function**: Processes HTTP requests, validates data using DTOs, calls services

#### 3. **Services Layer** (`src/services/`)
- **Responsibility**: Core business logic and orchestration
- **Files**: `*.service.ts` (e.g., `auth.service.ts`, `user.service.ts`)
- **Function**: Implements business rules, coordinates data operations

#### 4. **Data Access Layer** (`src/prisma/`, `src/repositories/`)
- **Responsibility**: Database operations and data persistence
- **Tools**: Prisma ORM for type-safe database access
- **Function**: Handles CRUD operations and complex queries. Repositories abstract the database logic, providing a clean API for services.

### Supporting Components

- **DTOs** (`src/dtos/`): Data validation and transformation objects
- **Middlewares** (`src/middlewares/`): Authentication, authorization, logging, error handling
- **Interfaces** (`src/interfaces/`): TypeScript type definitions
- **Utils** (`src/utils/`): Helper functions and utilities

## API Endpoints

For detailed information about the API endpoints, please refer to the [API.md](docs/API.md) file.

## Getting Started

### Prerequisites

- Node.js 18.x or higher
- PostgreSQL 13.x or higher
- npm or yarn

### Installation

1.  Clone the repository:

    ```bash
    git clone https://github.com/your-username/secure-backend.git
    ```

2.  Install the dependencies:

    ```bash
    npm install
    ```

3.  Set up the environment variables:

    ```bash
    cp .env.example .env.development.local
    ```

    Update the `.env.development.local` file with your local configuration.

4.  Run the database migrations:

    ```bash
    npm run db:migrate:dev
    ```

5.  Start the development server:

    ```bash
    npm run dev
    ```

The server will be running at `http://localhost:3000`.

## Usage

### API Documentation

Once the server is running, you can access the interactive API documentation at `http://localhost:3000/api-docs`.

### Authentication

To access the protected endpoints, you need to obtain a JWT token by registering a new user or logging in with an existing user. Once you have the token, you need to include it in the `Authorization` header of your requests as a Bearer token:

```
Authorization: Bearer <your-jwt-token>
```

## Contributing

Contributions are welcome! Please read the [CONTRIBUTING.md](CONTRIBUTING.md) file for more information.

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

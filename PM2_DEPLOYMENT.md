# PM2 Deployment Guide

This document provides comprehensive information about PM2 deployment configuration, scripts, and best practices for this Node.js/TypeScript application.

## Overview

The application uses PM2 for production process management with separate configurations for development and production environments. The ecosystem configuration supports:

- Environment-specific configuration
- Graceful shutdowns and reloads
- Health monitoring
- Log management
- Clustering for production
- Zero-downtime deployments

## Configuration Files

### ecosystem.config.js

The PM2 ecosystem configuration defines two main applications:

#### Development Configuration (`dev`)
- **Single instance** with fork mode
- **Memory limit**: 1GB
- **Environment file**: `.env.development.local`
- **Logs**: `./logs/dev-*.log`
- **Health check**: `http://localhost:3000/health`
- **Graceful shutdown**: 5s timeout
- **Auto-restart**: Up to 10 restarts with 4s delay

#### Production Configuration (`prod`)
- **Multi-instance** cluster mode (`instances: 'max'`)
- **Memory limit**: 2GB
- **Environment file**: `.env.production.local`
- **Logs**: `./logs/prod-*.log`
- **Health check**: `http://localhost:3000/health`
- **Graceful shutdown**: 10s timeout
- **Auto-restart**: Up to 5 restarts with 5s delay
- **Performance optimizations**: Node.js memory optimization flags
- **Zero-downtime reloads**: Enabled

## Environment Variables

Environment variables are automatically loaded from environment-specific files:

- **Development**: `.env.development.local`
- **Production**: `.env.production.local`

Required environment variables include:
- `NODE_ENV`
- `PORT`
- `DATABASE_URL`
- `JWT_SECRET`
- And other variables defined in `src/config/index.ts`

## Available Scripts

### Deployment Scripts

```bash
# Deploy to production (builds and starts)
npm run deploy:prod

# Deploy to development (builds and starts)
npm run deploy:dev
```

### PM2 Management Scripts

#### Process Control
```bash
# Graceful reload (zero-downtime)
npm run pm2:reload:prod
npm run pm2:reload:dev

# Stop processes
npm run pm2:stop:prod
npm run pm2:stop:dev

# Restart processes (with downtime)
npm run pm2:restart:prod
npm run pm2:restart:dev
```

#### Monitoring & Logs
```bash
# View logs
npm run pm2:logs:prod
npm run pm2:logs:dev

# Real-time monitoring dashboard
npm run pm2:monitor

# Process status
npm run pm2:status

# Clear log files
npm run pm2:flush
```

## Deployment Workflow

### First-time Setup

1. **Build the application**:
   ```bash
   npm run build
   ```

2. **Ensure logs directory exists**:
   ```bash
   mkdir -p logs
   ```

3. **Deploy to desired environment**:
   ```bash
   # For production
   npm run deploy:prod
   
   # For development
   npm run deploy:dev
   ```

### Updates and Redeployments

1. **Pull latest changes and rebuild**:
   ```bash
   git pull origin main
   npm install
   npm run build
   ```

2. **Graceful reload (recommended for production)**:
   ```bash
   npm run pm2:reload:prod
   ```

3. **Alternative: Full restart** (if graceful reload fails):
   ```bash
   npm run pm2:restart:prod
   ```

## Log Management

### Log Files Location
- Development logs: `./logs/dev-*.log`
- Production logs: `./logs/prod-*.log`

### Log Types
- **error_file**: Error logs only
- **out_file**: Standard output logs
- **log_file**: Combined logs (stdout + stderr)

### Log Rotation
- Production logs include timestamps and automatic log merging
- Use `npm run pm2:flush` to clear old logs
- Consider setting up logrotate for long-running production systems

## Health Monitoring

### Health Check Endpoint
The application exposes a health check at `/health` endpoint that PM2 uses for:
- Process health verification
- Automatic restart triggers
- Load balancer health checks

### Monitoring Dashboard
Access real-time monitoring:
```bash
npm run pm2:monitor
```

## Graceful Shutdown

The application implements graceful shutdown handling:

1. **SIGINT/SIGTERM signals** trigger graceful shutdown
2. **Active connections** are allowed to complete
3. **Database connections** are properly closed
4. **Timeout protection** prevents hanging processes

### Shutdown Timeouts
- **Development**: 5 seconds
- **Production**: 10 seconds

## Production Optimization

### Clustering
- Production uses all available CPU cores
- Load balancing across worker processes
- Automatic worker replacement on crashes

### Memory Management
- Memory limits prevent runaway processes
- Node.js heap size optimization in production
- Automatic restart on memory threshold

### Performance Monitoring
- PMX integration for advanced monitoring
- Real-time metrics collection
- Performance alerts and notifications

## Troubleshooting

### Common Issues

1. **Port already in use**:
   ```bash
   npm run pm2:stop:prod
   # or kill the process using the port
   lsof -ti:3000 | xargs kill -9
   ```

2. **Environment variables not loading**:
   - Verify `.env.production.local` or `.env.development.local` exists
   - Check file permissions
   - Validate environment variable syntax

3. **Application won't start**:
   ```bash
   # Check PM2 logs
   npm run pm2:logs:prod
   
   # Check PM2 status
   npm run pm2:status
   
   # Test application directly
   npm run start:dev
   ```

4. **Memory issues**:
   ```bash
   # Monitor memory usage
   npm run pm2:monitor
   
   # Restart if needed
   npm run pm2:restart:prod
   ```

### Debug Mode

For debugging PM2 issues:
```bash
# Start with verbose logging
pm2 start ecosystem.config.js --only prod --log-type json

# Real-time logs
pm2 logs prod --raw
```

## Best Practices

1. **Always build before deployment**
2. **Use graceful reloads for zero-downtime updates**
3. **Monitor logs regularly**
4. **Set up proper log rotation**
5. **Test deployments in development first**
6. **Keep environment files secure and backed up**
7. **Monitor application performance and memory usage**
8. **Use health checks for automated monitoring**

## Security Considerations

1. **Environment Files**: Never commit `.env.*.local` files to version control
2. **Log Files**: Ensure log files don't contain sensitive information
3. **Process Permissions**: Run PM2 processes with appropriate user permissions
4. **Network Security**: Secure health check endpoints if exposed externally

## Remote Deployment

The ecosystem configuration includes deployment setup for remote servers. Update the `deploy` section in `ecosystem.config.js` with your server details:

```javascript
deploy: {
  production: {
    user: 'your-user',
    host: 'your-server.com',
    ref: 'origin/main',
    repo: 'your-repo-url',
    path: '/path/to/deployment',
    'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production && pm2 save'
  }
}
```

Then deploy with:
```bash
pm2 deploy production setup
pm2 deploy production
```

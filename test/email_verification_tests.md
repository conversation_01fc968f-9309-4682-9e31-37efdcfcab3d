
# Email Verification Tests

This document outlines the `curl` commands for testing the email verification features of the application.

## Verify Email

### 1. Successful Email Verification

*   **Description:** A user should be able to verify their email with a valid token.
*   **Command:**
    ```bash
    curl -X POST \
      http://localhost:3000/api/v1/email-verification/verify \
      -H 'Content-Type: application/json' \
      -d '{
        "token": "<VERIFICATION_TOKEN>"
      }'
    ```
*   **Expected Outcome:** A `200 OK` response.

### 2. Email Verification with an Invalid Token

*   **Description:** The system should not allow email verification with an invalid token.
*   **Command:**
    ```bash
    curl -X POST \
      http://localhost:3000/api/v1/email-verification/verify \
      -H 'Content-Type: application/json' \
      -d '{
        "token": "invalidtoken"
      }'
    ```
*   **Expected Outcome:** A `400 Bad Request` response with an error message.

## Resend Verification Email

### 1. Successful Resend of Verification Email

*   **Description:** An authenticated user should be able to request a new verification email.
*   **Command:**
    ```bash
    curl -X POST \
      http://localhost:3000/api/v1/email-verification/resend \
      -H 'Authorization: Bearer <ACCESS_TOKEN>'
    ```
*   **Expected Outcome:** A `200 OK` response.

### 2. Unauthenticated Resend of Verification Email

*   **Description:** An unauthenticated user should be able to request a new verification email by providing their email address.
*   **Command:**
    ```bash
    curl -X POST \
      http://localhost:3000/api/v1/email-verification/resend \
      -H 'Content-Type: application/json' \
      -d '{
        "email": "<EMAIL>"
      }'
    ```
*   **Expected Outcome:** A `200 OK` response.

## Change Email

### 1. Initiate Email Change

*   **Description:** An authenticated user should be able to initiate an email change.
*   **Command:**
    ```bash
    curl -X POST \
      http://localhost:3000/api/v1/email-verification/change-email \
      -H 'Content-Type: application/json' \
      -H 'Authorization: Bearer <ACCESS_TOKEN>' \
      -d '{
        "newEmail": "<EMAIL>"
      }'
    ```
*   **Expected Outcome:** A `200 OK` response.


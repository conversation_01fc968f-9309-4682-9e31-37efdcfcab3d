
# Authentication Tests

This document outlines the `curl` commands for testing the authentication features of the application.

## User Registration

### 1. Successful Registration

*   **Description:** A new user should be able to register with a valid email and password.
*   **Command:**
    ```bash
    curl -X POST \
      http://localhost:3000/api/v1/auth/register \
      -H 'Content-Type: application/json' \
      -d '{
        "email": "<EMAIL>",
        "password": "Password123!",
        "username": "testuser"
      }'
    ```
*   **Expected Outcome:** A `201 Created` response with the user's information and a JWT token.

### 2. Registration with an Existing Email

*   **Description:** The system should prevent registration with an email that is already in use.
*   **Command:**
    ```bash
    curl -X POST \
      http://localhost:3000/api/v1/auth/register \
      -H 'Content-Type: application/json' \
      -d '{
        "email": "<EMAIL>",
        "password": "Password123!",
        "username": "testuser2"
      }'
    ```
*   **Expected Outcome:** A `409 Conflict` response with an error message.

### 3. Registration with a Weak Password

*   **Description:** The system should enforce password strength requirements.
*   **Command:**
    ```bash
    curl -X POST \
      http://localhost:3000/api/v1/auth/register \
      -H 'Content-Type: application/json' \
      -d '{
        "email": "<EMAIL>",
        "password": "weak",
        "username": "weakpassword"
      }'
    ```
*   **Expected Outcome:** A `400 Bad Request` response with an error message about the password not meeting the required criteria.

## User Login

### 1. Successful Login

*   **Description:** A registered user should be able to log in with correct credentials.
*   **Command:**
    ```bash
    curl -X POST \
      http://localhost:3000/api/v1/auth/login \
      -H 'Content-Type: application/json' \
      -d '{
        "email": "<EMAIL>",
        "password": "Password123!"
      }'
    ```
*   **Expected Outcome:** A `200 OK` response with a JWT token.

### 2. Login with Incorrect Credentials

*   **Description:** The system should prevent login with incorrect credentials.
*   **Command:**
    ```bash
    curl -X POST \
      http://localhost:3000/api/v1/auth/login \
      -H 'Content-Type: application/json' \
      -d '{
        "email": "<EMAIL>",
        "password": "WrongPassword!"
      }'
    ```
*   **Expected Outcome:** A `401 Unauthorized` response with an error message.

## User Logout

### 1. Successful Logout

*   **Description:** A logged-in user should be able to log out.
*   **Command:**
    ```bash
    curl -X POST \
      http://localhost:3000/api/v1/auth/logout \
      -H 'Authorization: Bearer <ACCESS_TOKEN>'
    ```
*   **Expected Outcome:** A `200 OK` response.

## Token Refresh

### 1. Successful Token Refresh

*   **Description:** A user with a valid refresh token should be able to get a new access token.
*   **Command:**
    ```bash
    curl -X POST \
      http://localhost:3000/api/v1/auth/refresh \
      -H 'Content-Type: application/json' \
      -d '{
        "refreshToken": "<REFRESH_TOKEN>"
      }'
    ```
*   **Expected Outcome:** A `200 OK` response with a new JWT access token.



# User Management Tests

This document outlines the `curl` commands for testing the user management features of the application.

## Get User Profile

### 1. Get Own User Profile

*   **Description:** An authenticated user should be able to retrieve their own profile.
*   **Command:**
    ```bash
    curl -X GET \
      http://localhost:3000/api/v1/users/profile \
      -H 'Authorization: Bearer <ACCESS_TOKEN>'
    ```
*   **Expected Outcome:** A `200 OK` response with the user's profile information.

## Update User Profile

### 1. Update Own User Profile

*   **Description:** An authenticated user should be able to update their own profile.
*   **Command:**
    ```bash
    curl -X PUT \
      http://localhost:3000/api/v1/users/<USER_ID> \
      -H 'Content-Type: application/json' \
      -H 'Authorization: Bearer <ACCESS_TOKEN>' \
      -d '{
        "firstName": "New",
        "lastName": "Name"
      }'
    ```
*   **Expected Outcome:** A `200 OK` response with the updated user profile.

## Admin User Management

### 1. Get All Users (Admin)

*   **Description:** An admin user should be able to retrieve a list of all users.
*   **Command:**
    ```bash
    curl -X GET \
      http://localhost:3000/api/v1/users \
      -H 'Authorization: Bearer <ADMIN_ACCESS_TOKEN>'
    ```
*   **Expected Outcome:** A `200 OK` response with a list of users.

### 2. Get User by ID (Admin)

*   **Description:** An admin user should be able to retrieve a user by their ID.
*   **Command:**
    ```bash
    curl -X GET \
      http://localhost:3000/api/v1/users/<USER_ID> \
      -H 'Authorization: Bearer <ADMIN_ACCESS_TOKEN>'
    ```
*   **Expected Outcome:** A `200 OK` response with the user's profile.

### 3. Update User (Admin)

*   **Description:** An admin user should be able to update any user's profile.
*   **Command:**
    ```bash
    curl -X PUT \
      http://localhost:3000/api/v1/users/<USER_ID> \
      -H 'Content-Type: application/json' \
      -H 'Authorization: Bearer <ADMIN_ACCESS_TOKEN>' \
      -d '{
        "firstName": "Updated",
        "lastName": "Name"
      }'
    ```
*   **Expected Outcome:** A `200 OK` response with the updated user profile.

### 4. Delete User (Admin)

*   **Description:** An admin user should be able to delete a user.
*   **Command:**
    ```bash
    curl -X DELETE \
      http://localhost:3000/api/v1/users/<USER_ID> \
      -H 'Authorization: Bearer <ADMIN_ACCESS_TOKEN>'
    ```
*   **Expected Outcome:** A `204 No Content` response.


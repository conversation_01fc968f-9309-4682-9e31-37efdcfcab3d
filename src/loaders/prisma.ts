import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

class PrismaLoader {
  private static instance: PrismaClient | null = null;

  /**
   * Get singleton PrismaClient instance
   */
  public static getInstance(): PrismaClient {
    if (!PrismaLoader.instance) {
      PrismaLoader.instance = new PrismaClient({
        log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
      });

      logger.info('PrismaClient instance created');
    }

    return PrismaLoader.instance;
  }

  /**
   * Connect to the database
   */
  public static async connect(): Promise<void> {
    try {
      const client = PrismaLoader.getInstance();
      await client.$connect();
      logger.info('Database connected successfully');
    } catch (error) {
      logger.error('Failed to connect to database:', error);
      throw error;
    }
  }

  /**
   * Graceful shutdown - disconnect from database
   */
  public static async disconnect(): Promise<void> {
    if (PrismaLoader.instance) {
      try {
        await PrismaLoader.instance.$disconnect();
        PrismaLoader.instance = null;
        logger.info('Database disconnected successfully');
      } catch (error) {
        logger.error('Error disconnecting from database:', error);
        throw error;
      }
    }
  }

  /**
   * Health check for database connection
   */
  public static async healthCheck(): Promise<boolean> {
    try {
      const client = PrismaLoader.getInstance();
      await client.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      logger.error('Database health check failed:', error);
      return false;
    }
  }
}

// Export the singleton instance
export const prisma = PrismaLoader.getInstance();

// Export the loader class for additional methods
export { PrismaLoader };

// Export helper functions for graceful shutdown
export const connectDatabase = PrismaLoader.connect;
export const disconnectDatabase = PrismaLoader.disconnect;
export const databaseHealthCheck = PrismaLoader.healthCheck;

// Setup graceful shutdown handlers
const gracefulShutdown = async (signal: string) => {
  logger.info(`Received ${signal}. Starting graceful shutdown...`);

  try {
    await PrismaLoader.disconnect();
    logger.info('Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error('Error during graceful shutdown:', error);
    process.exit(1);
  }
};

// Handle process termination signals
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

// Handle uncaught exceptions
process.on('uncaughtException', async error => {
  logger.error('Uncaught Exception:', error);
  await PrismaLoader.disconnect();
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', async (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  await PrismaLoader.disconnect();
  process.exit(1);
});

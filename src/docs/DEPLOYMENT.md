# Deployment Guide

This document provides a comprehensive guide to deploying the application, covering everything from environment setup to production best practices. It is intended to ensure a smooth and secure deployment process.

## Environment Variables

Before deploying, ensure that all required environment variables are set in a `.env` file. These variables include database connection strings, JWT secrets, and other sensitive information. A `.env.example` file is provided as a template.

## Building the Application

To build the application for production, run the following command:

```bash
npm run build
```

This command transpiles the TypeScript code to JavaScript and places the output in the `dist` directory.

## Running in Production

It is recommended to use a process manager like PM2 to run the application in production. PM2 provides features like automatic restarts, clustering, and logging.

To start the application with PM2, run:

```bash
npm run start:prod
```

This command starts the application in cluster mode, which will automatically scale the application across all available CPU cores.

## Health Checks

The application includes a health check endpoint at `/health`, which can be used to monitor the application's status. This endpoint returns a `200 OK` response if the application is running correctly.
import 'reflect-metadata';
import http from 'http';
import { PORT, NODE_ENV } from './config';
import { logger } from './utils/logger';
import { validateEnv } from './utils/validateEnv';
import app from './app';

// Validate environment variables
validateEnv();

// Create HTTP server
const server = http.createServer(app);

// Port configuration
const port = PORT || 3000;
const env = NODE_ENV || 'development';

// Start server
server.listen(port, () => {
  logger.info(`🚀 Server running in ${env} mode on port ${port}`);
  logger.info(`📚 API Documentation available at http://localhost:${port}/api-docs`);
  logger.info(`❤️ Health check available at http://localhost:${port}/health`);
});

// Graceful shutdown handlers
const gracefulShutdown = (signal: string) => {
  logger.info(`${signal} received, shutting down gracefully`);

  server.close(error => {
    if (error) {
      logger.error('Error during server shutdown:', error);
      process.exit(1);
    }

    logger.info('✅ Server closed successfully');
    process.exit(0);
  });

  // Force shutdown after timeout
  setTimeout(() => {
    logger.error('⚠️ Forceful shutdown initiated');
    process.exit(1);
  }, 10000); // 10 seconds timeout
};

// Handle unexpected errors
const unexpectedErrorHandler = (error: unknown) => {
  logger.error('Unexpected error occurred:', error);
  gracefulShutdown('UNEXPECTED_ERROR');
};

// Process event listeners
process.on('uncaughtException', error => {
  logger.error('Uncaught Exception:', error);
  unexpectedErrorHandler(error);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  unexpectedErrorHandler(reason);
});

// Graceful shutdown signals
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

// Handle process warnings
process.on('warning', warning => {
  logger.warn('Process warning:', warning);
});

export { server };
export default server;

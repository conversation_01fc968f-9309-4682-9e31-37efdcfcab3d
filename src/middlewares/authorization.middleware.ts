import { Request, Response, NextFunction } from 'express';
import { Role } from '@prisma/client';
import { HttpException } from '../exceptions/HttpException';
import { User as UserInterface } from '../interfaces/user.interface';
import { RBACService } from '../services/rbac.service';
import { Resource, Action } from '../types/rbac';
import { User } from '../interfaces/user.interface';

// Legacy role-based middleware (for backward compatibility)
const authorizationMiddleware = (roles: Role[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { user } = req;

    if (!user || !roles.includes((user as UserInterface).role)) {
      return next(new HttpException(403, 'Forbidden'));
    }

    next();
  };
};

// Enhanced permission-based middleware
const permissionMiddleware = (resource: Resource, action: Action, getResourceData?: (req: Request) => any) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { user } = req;

    if (!user) {
      return next(new HttpException(401, 'Authentication required'));
    }

    // Get resource data if function is provided
    const resourceData = getResourceData ? getResourceData(req) : undefined;

    // Check permission
    const permissionResult = RBACService.checkPermission(user as User, resource, action, resourceData);

    if (!permissionResult.allowed) {
      return next(new HttpException(403, permissionResult.reason || 'Insufficient permissions'));
    }

    next();
  };
};

// Middleware to check if user can access any part of a resource
const resourceAccessMiddleware = (resource: Resource) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { user } = req;

    if (!user) {
      return next(new HttpException(401, 'Authentication required'));
    }

    if (!RBACService.canAccessResource(user as User, resource)) {
      return next(new HttpException(403, `Access denied to resource: ${resource}`));
    }

    next();
  };
};

export default authorizationMiddleware;
export { permissionMiddleware, resourceAccessMiddleware };

import { Request, Response, NextFunction } from 'express';
import { RateLimiterRedis } from 'rate-limiter-flexible';
import * as DOMPurify from 'isomorphic-dompurify';
import validator from 'validator';
import { HttpException } from '../exceptions/HttpException';
import { logger } from '../utils/logger';
import { LoggingContext } from '../types/template';
import { redis } from '../config/redis';
import { User as UserInterface } from '../interfaces/user.interface';

// Rate limiter response interface
interface RateLimiterResponse {
  msBeforeNext?: number;
  remainingPoints?: number;
  totalHits?: number;
}

// Sanitized object type
type SanitizedObject = Record<string, unknown>;

// Redis-based distributed rate limiting for different endpoints
const loginLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'rl_login',
  points: 5, // Number of attempts
  duration: 15 * 60, // Per 15 minutes
  blockDuration: 15 * 60, // Block for 15 minutes
  execEvenly: true, // Spread requests evenly across duration
});

const registerLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'rl_register',
  points: 3, // Number of registration attempts
  duration: 60 * 60, // Per hour
  blockDuration: 60 * 60, // Block for 1 hour
  execEvenly: true,
});

const passwordChangeLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'rl_password_change',
  points: 3, // Number of password change attempts
  duration: 60 * 60, // Per hour
  blockDuration: 30 * 60, // Block for 30 minutes
  execEvenly: true,
});

const emailVerificationLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'rl_email_verification',
  points: 5, // Number of email verification requests
  duration: 60 * 60, // Per hour
  blockDuration: 15 * 60, // Block for 15 minutes
  execEvenly: true,
});

// General API rate limiter for all endpoints
const generalApiLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'rl_api_general',
  points: 100, // Number of requests
  duration: 60, // Per minute
  blockDuration: 60, // Block for 1 minute
  execEvenly: true,
});

// Strict rate limiter for sensitive operations
const strictLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'rl_strict',
  points: 10, // Number of requests
  duration: 60, // Per minute
  blockDuration: 5 * 60, // Block for 5 minutes
  execEvenly: true,
});

/**
 * Enhanced login rate limiter with progressive delays
 */
export const loginRateLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const key = `${req.ip || 'unknown'}_${req.body?.email || 'unknown'}`;
    await loginLimiter.consume(key);
    next();
  } catch (rateLimiterRes: unknown) {
    // Check if this is a rate limit error (has msBeforeNext) or a Redis connection error
    const rateLimiterError = rateLimiterRes as RateLimiterResponse;
    if (rateLimiterError && typeof rateLimiterError.msBeforeNext === 'number') {
      // This is a rate limit violation
      const secs = Math.round(rateLimiterError.msBeforeNext / 1000) || 1;

      logger.warn('Login rate limit exceeded', {
        ip: req.ip || 'unknown',
        email: req.body?.email || 'unknown',
        remainingPoints: rateLimiterError.remainingPoints || 0,
        msBeforeNext: rateLimiterError.msBeforeNext || 0,
      } as LoggingContext);

      res.set('Retry-After', String(secs));
      next(new HttpException(429, `Too many login attempts. Try again in ${secs} seconds.`));
    } else {
      // This is likely a Redis connection error or other system error
      logger.error('Login rate limiter error', {
        ip: req.ip || 'unknown',
        email: req.body?.email || 'unknown',
        error: rateLimiterRes instanceof Error ? rateLimiterRes.message : 'Unknown error',
      } as LoggingContext);

      next(new HttpException(500, 'Internal server error'));
    }
  }
};

/**
 * Registration rate limiter
 */
export const registerRateLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const key = req.ip || 'unknown';
    await registerLimiter.consume(key);
    next();
  } catch (rateLimiterRes: unknown) {
    const rateLimiterError = rateLimiterRes as RateLimiterResponse;
    const secs = Math.round(rateLimiterError.msBeforeNext! / 1000) || 1;

    logger.warn('Registration rate limit exceeded', {
      ip: req.ip || 'unknown',
      remainingPoints: rateLimiterError.remainingPoints || 0,
    } as LoggingContext);

    res.set('Retry-After', String(secs));
    next(new HttpException(429, `Too many registration attempts. Try again in ${secs} seconds.`));
  }
};

/**
 * Password change rate limiter
 */
export const passwordChangeRateLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const user = req.user as UserInterface;
    const key = `${req.ip || 'unknown'}_${user?.id || 'unknown'}`;
    await passwordChangeLimiter.consume(key);
    next();
  } catch (rateLimiterRes: unknown) {
    const rateLimiterError = rateLimiterRes as RateLimiterResponse;
    const secs = Math.round(rateLimiterError.msBeforeNext! / 1000) || 1;
    const user = req.user as UserInterface;

    logger.warn('Password change rate limit exceeded', {
      ip: req.ip || 'unknown',
      userId: user?.id || 'unknown',
      remainingPoints: rateLimiterError.remainingPoints || 0,
    } as LoggingContext);

    res.set('Retry-After', String(secs));
    next(new HttpException(429, `Too many password change attempts. Try again in ${secs} seconds.`));
  }
};

/**
 * Email verification rate limiter
 */
export const emailVerificationRateLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Use IP and email/userId for rate limiting key
    const user = req.user as UserInterface;
    const email = req.body?.email || user?.email || 'unknown';
    const key = `${req.ip || 'unknown'}_${email}`;
    await emailVerificationLimiter.consume(key);
    next();
  } catch (rateLimiterRes: unknown) {
    const rateLimiterError = rateLimiterRes as RateLimiterResponse;
    const secs = Math.round(rateLimiterError.msBeforeNext! / 1000) || 1;
    const user = req.user as UserInterface;

    logger.warn('Email verification rate limit exceeded', {
      ip: req.ip || 'unknown',
      email: req.body?.email || user?.email || 'unknown',
      remainingPoints: rateLimiterError.remainingPoints || 0,
    } as LoggingContext);

    res.set('Retry-After', String(secs));
    next(new HttpException(429, `Too many email verification requests. Try again in ${secs} seconds.`));
  }
};

/**
 * Security headers middleware
 */
export const securityHeaders = (_req: Request, res: Response, next: NextFunction) => {
  // Remove potentially sensitive headers
  res.removeHeader('X-Powered-By');

  // Add security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

  next();
};

/**
 * Enhanced input sanitization middleware using DOMPurify and validator.js
 * Provides comprehensive protection against XSS, injection attacks, and malicious input
 */
export const sanitizeInput = (req: Request, _res: Response, next: NextFunction) => {
  try {
    const sanitizeString = (value: string): string => {
      if (typeof value !== 'string') {
        return value;
      }

      // Trim whitespace
      let sanitized = value.trim();

      // Limit string length to prevent DoS attacks
      if (sanitized.length > 10000) {
        logger.warn('Input rejected due to excessive length', {
          originalLength: value.length,
          ip: req.ip,
        });
        throw new HttpException(400, 'Input too long');
      }

      // Check for SQL injection patterns and remove them
      const sqlInjectionPatterns = [
        /UNION\s+SELECT/gi,
        /DROP\s+TABLE/gi,
        /DELETE\s+FROM/gi,
        /INSERT\s+INTO/gi,
        /UPDATE\s+SET/gi,
        /ALTER\s+TABLE/gi,
        /CREATE\s+TABLE/gi,
        /EXEC\s*\(/gi,
        /EXECUTE\s*\(/gi,
        /--/g,
        /\/\*[\s\S]*?\*\//g, // SQL comments
      ];

      sqlInjectionPatterns.forEach(pattern => {
        sanitized = sanitized.replace(pattern, '');
      });

      // Check if the input contains actual HTML tags or JavaScript URLs
      const hasHtmlTags = /<[a-zA-Z][^>]*>/g.test(sanitized);
      const hasJavaScriptUrl = /javascript:/gi.test(sanitized);

      if (hasHtmlTags || hasJavaScriptUrl) {
        // Use DOMPurify to remove dangerous HTML content (like script tags and javascript: URLs)
        sanitized = DOMPurify.sanitize(sanitized, {
          ALLOWED_TAGS: [], // No HTML tags allowed
          ALLOWED_ATTR: [], // No attributes allowed
          KEEP_CONTENT: true, // Keep text content
          RETURN_DOM: false,
          RETURN_DOM_FRAGMENT: false,
        });
      }

      // Always HTML escape special characters (like &, <, >)
      sanitized = validator.escape(sanitized);

      // Additional security checks
      if (sanitized !== value) {
        logger.warn('Potentially malicious input detected and sanitized', {
          originalLength: value.length,
          sanitizedLength: sanitized.length,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          path: req.path,
        });
      }

      return sanitized;
    };

    const sanitizeObject = (obj: unknown, depth: number = 0): unknown => {
      // Prevent DoS attacks from deeply nested objects
      if (depth > 50) {
        throw new HttpException(400, 'Object structure too deep');
      }

      if (obj === null || obj === undefined) {
        return obj;
      }

      if (typeof obj === 'string') {
        return sanitizeString(obj);
      }

      if (typeof obj === 'number' || typeof obj === 'boolean') {
        return obj;
      }

      if (Array.isArray(obj)) {
        return obj.map(item => sanitizeObject(item, depth + 1));
      }

      if (typeof obj === 'object') {
        const sanitized: SanitizedObject = {};
        for (const [key, value] of Object.entries(obj)) {
          // Sanitize both key and value
          const sanitizedKey = sanitizeString(key);
          sanitized[sanitizedKey] = sanitizeObject(value, depth + 1);
        }
        return sanitized;
      }

      return obj;
    };

    // Sanitize request body
    if (req.body) {
      req.body = sanitizeObject(req.body);
    }

    // Sanitize query parameters
    if (req.query && Object.keys(req.query).length > 0) {
      // Create a new sanitized query object
      const sanitizedQuery: SanitizedObject = {};
      for (const [key, value] of Object.entries(req.query)) {
        const sanitizedKey = sanitizeString(key);
        const sanitizedValue = sanitizeObject(value);
        sanitizedQuery[sanitizedKey] = sanitizedValue;
      }

      // Replace the entire query object using Object.defineProperty
      Object.defineProperty(req, 'query', {
        value: sanitizedQuery,
        writable: true,
        enumerable: true,
        configurable: true,
      });
    }

    // Sanitize URL parameters
    if (req.params && Object.keys(req.params).length > 0) {
      // Create a new sanitized params object
      const sanitizedParams: SanitizedObject = {};
      for (const [key, value] of Object.entries(req.params)) {
        const sanitizedKey = sanitizeString(key);
        const sanitizedValue = sanitizeString(value as string);
        sanitizedParams[sanitizedKey] = sanitizedValue;
      }

      // Replace the entire params object using Object.defineProperty
      Object.defineProperty(req, 'params', {
        value: sanitizedParams,
        writable: true,
        enumerable: true,
        configurable: true,
      });
    }

    next();
  } catch (error) {
    logger.error('Input sanitization failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      ip: req.ip,
      path: req.path,
      method: req.method,
    });

    // If it's already an HttpException, pass it through
    if (error instanceof HttpException) {
      next(error);
    } else {
      // Fail secure - reject the request if sanitization fails
      next(new HttpException(400, 'Invalid input format'));
    }
  }
};

/**
 * Account lockout tracking
 */
const accountLockouts = new Map<string, { attempts: number; lockedUntil?: Date }>();

export const trackFailedLogin = (email: string) => {
  const current = accountLockouts.get(email) || { attempts: 0 };
  current.attempts += 1;

  // Lock account after 5 failed attempts for 30 minutes
  if (current.attempts >= 5) {
    current.lockedUntil = new Date(Date.now() + 30 * 60 * 1000);
    logger.warn('Account locked due to failed login attempts', {
      email,
      attempts: current.attempts,
      lockedUntil: current.lockedUntil,
    } as LoggingContext);
  }

  accountLockouts.set(email, current);
};

export const trackSuccessfulLogin = (email: string) => {
  // Clear failed attempts on successful login
  accountLockouts.delete(email);
};

export const checkAccountLockout = (email: string): boolean => {
  const account = accountLockouts.get(email);
  if (!account || !account.lockedUntil) {
    return false;
  }

  // Check if lockout has expired
  if (new Date() > account.lockedUntil) {
    accountLockouts.delete(email);
    return false;
  }

  return true;
};

/**
 * IP-based suspicious activity detection
 */
const suspiciousIPs = new Map<string, { score: number; lastActivity: Date }>();

export const detectSuspiciousActivity = (req: Request, _res: Response, next: NextFunction) => {
  const ip = req.ip || 'unknown';
  const now = new Date();

  const activity = suspiciousIPs.get(ip) || { score: 0, lastActivity: now };

  // Reset score if last activity was more than 1 hour ago
  if (now.getTime() - activity.lastActivity.getTime() > 60 * 60 * 1000) {
    activity.score = 0;
  }

  // Increase score for certain suspicious patterns
  const userAgent = req.get('User-Agent') || '';
  const referer = req.get('Referer') || '';

  // Check for bot-like behavior
  if (!userAgent || userAgent.length < 10 || /bot|crawler|spider/i.test(userAgent)) {
    activity.score += 2;
  }

  // Check for rapid requests (if called multiple times quickly)
  if (now.getTime() - activity.lastActivity.getTime() < 1000) {
    activity.score += 1;
  }

  activity.lastActivity = now;
  suspiciousIPs.set(ip, activity);

  // Block if score is too high
  if (activity.score > 10) {
    logger.warn('Suspicious activity detected, blocking IP', {
      ip,
      score: activity.score,
      userAgent,
      referer,
    } as LoggingContext);

    return next(new HttpException(429, 'Suspicious activity detected. Access temporarily blocked.'));
  }

  next();
};

/**
 * Password strength validator middleware
 */
export const validatePasswordStrength = (req: Request, _res: Response, next: NextFunction) => {
  const { password, newPassword } = req.body || {};
  const passwordToCheck = newPassword || password;

  if (!passwordToCheck) {
    return next();
  }

  // Import password validation from utils
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const { PasswordUtils } = require('../utils/password');
  const validation = PasswordUtils.validatePasswordStrength(passwordToCheck);

  if (!validation.isValid) {
    return next(
      new HttpException(400, `Password does not meet security requirements: ${validation.feedback.join(', ')}`),
    );
  }

  next();
};

/**
 * Device fingerprinting (basic implementation)
 */
export const deviceFingerprint = (req: Request, _res: Response, next: NextFunction) => {
  const userAgent = req.get('User-Agent') || '';
  const acceptLanguage = req.get('Accept-Language') || '';
  const acceptEncoding = req.get('Accept-Encoding') || '';

  // Create a simple device fingerprint
  const fingerprint = Buffer.from(`${userAgent}:${acceptLanguage}:${acceptEncoding}`).toString('base64');

  // Store fingerprint in request for potential use
  (req as unknown as Record<string, unknown>).deviceFingerprint = fingerprint;

  next();
};

/**
 * General API rate limiting middleware
 */
export const generalApiRateLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const key = req.ip || 'unknown';
    await generalApiLimiter.consume(key);
    next();
  } catch (rateLimiterRes: unknown) {
    // Check if this is a rate limit error (has msBeforeNext) or a Redis connection error
    const rateLimiterError = rateLimiterRes as RateLimiterResponse;
    if (rateLimiterError && typeof rateLimiterError.msBeforeNext === 'number') {
      // This is a rate limit violation
      const secs = Math.round(rateLimiterError.msBeforeNext / 1000) || 1;

      logger.warn('General API rate limit exceeded', {
        ip: req.ip,
        path: req.path,
        method: req.method,
        retryAfter: secs,
      });

      res.set('Retry-After', String(secs));
      next(new HttpException(429, `Too many requests. Try again in ${secs} seconds.`));
    } else {
      // This is likely a Redis connection error or other system error
      logger.error('Rate limiter error', {
        ip: req.ip,
        path: req.path,
        method: req.method,
        error: rateLimiterRes instanceof Error ? rateLimiterRes.message : 'Unknown error',
      });

      next(new HttpException(500, 'Internal server error'));
    }
  }
};

/**
 * Strict rate limiting for sensitive operations
 */
export const strictRateLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const user = req.user as UserInterface;
    const key = `${req.ip || 'unknown'}_${user?.id || 'anonymous'}`;
    await strictLimiter.consume(key);
    next();
  } catch (rateLimiterRes: unknown) {
    const rateLimiterError = rateLimiterRes as RateLimiterResponse;
    const secs = Math.round(rateLimiterError.msBeforeNext! / 1000) || 1;
    const user = req.user as UserInterface;

    logger.warn('Strict rate limit exceeded', {
      ip: req.ip,
      userId: user?.id,
      path: req.path,
      method: req.method,
      retryAfter: secs,
    });

    res.set('Retry-After', String(secs));
    next(new HttpException(429, `Rate limit exceeded for sensitive operation. Try again in ${secs} seconds.`));
  }
};

/**
 * Get rate limiting statistics - Express middleware
 */
export const getRateLimitStats = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const ip = req.ip || 'unknown';

    // Get stats from different rate limiters using Promise.allSettled to handle individual failures
    const [loginStats, registerStats, generalStats] = await Promise.allSettled([
      loginLimiter.get(`${ip}_unknown`),
      registerLimiter.get(ip),
      generalApiLimiter.get(ip),
    ]);

    // Check if any of the calls failed (rejected promises indicate errors)
    const hasError = [loginStats, registerStats, generalStats].some(result => result.status === 'rejected');

    if (hasError) {
      // If there's a rejection, treat it as an error
      const firstError = [loginStats, registerStats, generalStats].find(
        result => result.status === 'rejected',
      ) as PromiseRejectedResult;

      throw firstError.reason;
    }

    // Process results and provide defaults
    const processStats = (result: PromiseSettledResult<unknown>, defaultRemaining: number) => {
      if (result.status === 'fulfilled' && result.value) {
        const rateLimiterResult = result.value as RateLimiterResponse;
        return {
          remaining: rateLimiterResult.remainingPoints || defaultRemaining,
          resetTime: rateLimiterResult.msBeforeNext
            ? new Date(Date.now() + rateLimiterResult.msBeforeNext).toISOString()
            : null,
        };
      }
      return {
        remaining: defaultRemaining,
        resetTime: null,
      };
    };

    const stats = {
      limits: {
        login: processStats(loginStats, 5),
        register: processStats(registerStats, 3),
        general: processStats(generalStats, 100),
      },
      ip,
      timestamp: new Date().toISOString(),
    };

    res.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    logger.error('Failed to retrieve rate limit statistics', {
      ip: req.ip,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    next(new HttpException(500, 'Failed to retrieve rate limit statistics'));
  }
};

// Export all middlewares
export default {
  loginRateLimit,
  registerRateLimit,
  passwordChangeRateLimit,
  emailVerificationRateLimit,
  generalApiRateLimit,
  strictRateLimit,
  securityHeaders,
  sanitizeInput,
  trackFailedLogin,
  trackSuccessfulLogin,
  checkAccountLockout,
  detectSuspiciousActivity,
  validatePasswordStrength,
  deviceFingerprint,
  getRateLimitStats,
};

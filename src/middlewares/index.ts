export { default as authMiddleware } from './auth.middleware';
export {
  default as authorizationMiddleware,
  permissionMiddleware,
  resourceAccessMiddleware,
} from './authorization.middleware';
export { default as ensureEmailVerified } from './emailVerification.middleware';
export { default as errorMiddleware } from '../middleware/error.middleware';
export { default as loggerMiddleware } from './logger.middleware';
export { default as responseLoggerMiddleware } from './responseLogger.middleware';
export { default as rateLimiterMiddleware } from './rateLimiter.middleware';
export { default as validationMiddleware } from './validation.middleware';

// Import security middlewares
import {
  loginRateLimit,
  registerRateLimit,
  passwordChangeRateLimit,
  emailVerificationRateLimit,
  securityHeaders,
  sanitizeInput,
  detectSuspiciousActivity,
  validatePasswordStrength,
  deviceFingerprint,
} from './security.middleware';

// Export security middlewares
export {
  loginRateLimit,
  registerRateLimit,
  passwordChangeRateLimit,
  emailVerificationRateLimit,
  securityHeaders,
  sanitizeInput,
  detectSuspiciousActivity,
  validatePasswordStrength,
  deviceFingerprint,
};

import { plainToInstance } from 'class-transformer';
import { validate, ValidationError } from 'class-validator';
import { Request, Response, NextFunction } from 'express';

function validationMiddleware(
  type: new () => object,
  skipMissingProperties = false,
): (req: Request, res: Response, next: NextFunction) => void {
  return (req, res, next) => {
    const dto = plainToInstance(type, req.body);

    validate(dto, { skipMissingProperties }).then((errors: ValidationError[]) => {
      if (errors.length > 0) {
        const message = errors.map((error: ValidationError) => Object.values(error.constraints || {})).join(', ');
        next(new Error(message));
      } else {
        next();
      }
    });
  };
}

export default validationMiddleware;

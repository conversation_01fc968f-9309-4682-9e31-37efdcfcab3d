import { Request, Response, NextFunction } from 'express';
import { SessionVersioningService } from '../services/sessionVersioning.service';
import { HttpException } from '../exceptions/HttpException';
import { logger } from '../utils/secureLogger';
import { User as UserInterface } from '../interfaces/user.interface';

/**
 * Session versioning middleware
 * Validates session versions and handles session invalidation
 */
export const validateSessionVersion = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Skip validation for public routes
    if (!req.session || !req.session.userId) {
      return next();
    }

    // Update session activity
    SessionVersioningService.updateSessionActivity(req);

    // Validate session version
    const isValid = await SessionVersioningService.validateSessionVersion(req);

    if (!isValid) {
      // Session version is invalid - invalidate and redirect to login
      try {
        await SessionVersioningService.invalidateSession(req, res);
      } catch (invalidationError) {
        logger.error('Session invalidation failed', {
          userId: req.session.userId,
          error: invalidationError instanceof Error ? invalidationError.message : 'Unknown error',
        });
        // Continue with the 401 response even if invalidation fails
      }

      logger.warn('Invalid session version detected, session invalidated', {
        userId: req.session.userId,
        sessionId: req.sessionID?.substring(0, 8) + '...',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      return next(new HttpException(401, 'Session expired due to security changes. Please log in again.'));
    }

    next();
  } catch (error) {
    logger.error('Session version validation failed', {
      userId: req.session?.userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    // Fail secure - invalidate session on error
    if (req.session && req.session.userId) {
      try {
        await SessionVersioningService.invalidateSession(req, res);
      } catch (invalidationError) {
        logger.error('Session invalidation failed during error handling', {
          userId: req.session.userId,
          error: invalidationError instanceof Error ? invalidationError.message : 'Unknown error',
        });
        // Continue with the 401 response even if invalidation fails
      }
    }

    next(new HttpException(401, 'Session validation failed. Please log in again.'));
  }
};

/**
 * Initialize session versioning for authenticated users
 */
export const initializeSessionVersioning = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // This middleware should be called after successful authentication
    // The user object should be available in res.locals or req.user
    const user = res.locals.user || req.user;

    if (user && req.session) {
      await SessionVersioningService.initializeSession(req, user);
    }

    next();
  } catch (error) {
    logger.error('Failed to initialize session versioning', {
      userId: (res.locals.user as UserInterface)?.id || (req.user as UserInterface)?.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    // Don't fail the request, just log the error
    next();
  }
};

/**
 * Middleware to handle role changes
 */
export const handleRoleChange = (oldRole: string, newRole: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (req.session && req.session.userId) {
        await SessionVersioningService.handleRoleChange(req.session.userId, oldRole, newRole);

        // Update session role
        req.session.userRole = newRole;
        req.session.lastRoleChange = new Date().toISOString();
      }

      next();
    } catch (error) {
      logger.error('Failed to handle role change in middleware', {
        userId: req.session?.userId,
        oldRole,
        newRole,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      // Don't fail the request, but log the error
      next();
    }
  };
};

/**
 * Middleware to handle password changes
 */
export const handlePasswordChange = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (req.session && req.session.userId) {
      await SessionVersioningService.handlePasswordChange(req.session.userId);
    }

    next();
  } catch (error) {
    logger.error('Failed to handle password change in middleware', {
      userId: req.session?.userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    // Don't fail the request, but log the error
    next();
  }
};

/**
 * Middleware to force session invalidation
 */
export const forceSessionInvalidation = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (req.session && req.session.userId) {
      await SessionVersioningService.invalidateSession(req, res);
    }

    next();
  } catch (error) {
    logger.error('Failed to force session invalidation', {
      userId: req.session?.userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    next();
  }
};

/**
 * Get session statistics endpoint middleware
 */
export const getSessionStats = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.session || !req.session.userId) {
      return next(new HttpException(401, 'No active session'));
    }

    const stats = await SessionVersioningService.getSessionStats(req.session.userId);

    res.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    logger.error('Failed to get session statistics', {
      userId: req.session?.userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    next(new HttpException(500, 'Failed to retrieve session statistics'));
  }
};

export { SessionVersioningService };

# Email Templates

This directory contains responsive, accessible HTML email templates with fallback text versions for various email communications.

## Templates

### 1. Verification Email (`verification.html` / `verification.txt`)
Used for new user email verification during account registration.

### 2. Email Change Verification (`emailChange.html` / `emailChange.txt`)
Used when users request to change their email address.

## Template Features

### 🎨 Design & Branding
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Dark Mode Support**: Automatically adapts to user's preferred color scheme
- **High Contrast Support**: Enhanced visibility for accessibility
- **Modern Gradient Headers**: Beautiful visual appeal with customizable colors
- **Logo Placeholder**: Easy integration of brand logos
- **Consistent Typography**: Uses system fonts for optimal rendering

### ♿ Accessibility Features
- **ARIA Labels**: Proper semantic markup for screen readers
- **Role Attributes**: Clear content structure (banner, main, contentinfo)
- **High Contrast Mode**: Support for users with visual impairments
- **Reduced Motion**: Respects user's motion preferences
- **Keyboard Navigation**: Fully accessible button and link elements
- **Alt Text**: Comprehensive alternative text for images

### 📱 Responsive Features
- **Mobile-First**: Optimized for mobile email clients
- **Fluid Layout**: Adapts to various screen sizes
- **Touch-Friendly**: Large buttons and touch targets
- **Email Client Compatibility**: Works across major email clients
- **MSO Conditional Comments**: Outlook-specific optimizations

## Template Variables

### Core Variables (Both Templates)
```
{{APP_NAME}}                - Application name
{{APP_NAME_INITIAL}}        - First letter of app name (for logo fallback)
{{USER_EMAIL}}              - User's email address
{{VERIFICATION_URL}}        - Verification link URL
{{EXPIRES_IN}}              - Token expiration time
{{CURRENT_YEAR}}            - Current year for copyright
```

### Branding Variables
```
{{LOGO_URL}}                - Company logo URL (optional)
{{COMPANY_ADDRESS}}         - Company physical address (optional)
{{SUPPORT_EMAIL}}           - Support email address
{{SUPPORT_URL}}             - Support center URL
{{PRIVACY_URL}}             - Privacy policy URL
{{TERMS_URL}}               - Terms of service URL
{{ACCOUNT_URL}}             - Account settings URL
```

### Verification Template Specific
```
{{VERIFICATION_TITLE}}      - Email subject/title
{{VERIFICATION_MESSAGE}}    - Main message content
```

### Email Change Template Specific
```
{{CURRENT_EMAIL}}           - User's current email address
{{REQUEST_DATE}}            - Date of email change request
```

## Usage Example

```typescript
// Example usage in email service
const templateVariables = {
  APP_NAME: 'Secure Backend API',
  APP_NAME_INITIAL: 'S',
  USER_EMAIL: '<EMAIL>',
  VERIFICATION_URL: 'https://app.com/verify?token=abc123',
  VERIFICATION_TITLE: 'Email Verification',
  VERIFICATION_MESSAGE: 'Thank you for registering!',
  EXPIRES_IN: '48 hours',
  CURRENT_YEAR: '2024',
  LOGO_URL: 'https://app.com/logo.png',
  COMPANY_ADDRESS: '123 Main St, City, State 12345',
  SUPPORT_EMAIL: '<EMAIL>',
  SUPPORT_URL: 'https://app.com/support',
  PRIVACY_URL: 'https://app.com/privacy',
  TERMS_URL: 'https://app.com/terms',
  ACCOUNT_URL: 'https://app.com/account'
};
```

## Customization

### Color Scheme
Both templates use CSS custom properties that can be easily customized:

**Verification Template**: Blue/Purple gradient theme
- Primary: `#667eea` to `#764ba2`
- Accent: `#2196f3`

**Email Change Template**: Orange gradient theme  
- Primary: `#e67e22` to `#d35400`
- Alert: `#e74c3c`

### Logo Integration
Templates support both image logos and text fallbacks:
- If `{{LOGO_URL}}` is provided, displays the image
- If not provided, shows `{{APP_NAME_INITIAL}}` as a styled letter

### Conditional Content
Templates use Mustache-style conditional blocks:
```html
{{#LOGO_URL}}
  <img src="{{LOGO_URL}}" alt="Logo">
{{/LOGO_URL}}
{{^LOGO_URL}}
  {{APP_NAME_INITIAL}}
{{/LOGO_URL}}
```

## Email Client Compatibility

### Tested and Optimized For:
- ✅ Gmail (Web, iOS, Android)
- ✅ Outlook (Desktop, Web, iOS, Android)
- ✅ Apple Mail (macOS, iOS)
- ✅ Yahoo Mail
- ✅ Thunderbird
- ✅ Mobile clients (iOS Mail, Android Gmail)

### Technical Specifications:
- **Max Width**: 600px (optimal for email clients)
- **Inline CSS**: Critical styles inlined for better compatibility
- **Table-based Layout**: Ensures consistent rendering
- **Progressive Enhancement**: Graceful degradation in older clients

## File Structure
```
src/emails/templates/
├── verification.html       # HTML template for email verification
├── verification.txt        # Plain text fallback for email verification
├── emailChange.html        # HTML template for email change verification
├── emailChange.txt         # Plain text fallback for email change
└── README.md              # This documentation file
```

## Best Practices

1. **Always provide text fallbacks** - Include both HTML and text versions
2. **Test across clients** - Verify rendering in major email clients
3. **Optimize images** - Use optimized images with proper alt text
4. **Keep it simple** - Avoid complex layouts that may break
5. **Mobile-first** - Design for mobile, enhance for desktop
6. **Accessibility first** - Include proper ARIA labels and semantic markup

## Security Considerations

- **Token Expiration**: Always include expiration information
- **HTTPS URLs**: Use secure URLs for all links
- **Clear Instructions**: Provide clear security warnings
- **Contact Information**: Include support contact for security issues

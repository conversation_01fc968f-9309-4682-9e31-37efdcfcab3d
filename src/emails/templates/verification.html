<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=no,address=no,email=no,date=no,url=no">
    <meta name="x-apple-disable-message-reformatting">
    <meta name="color-scheme" content="light dark">
    <meta name="supported-color-schemes" content="light dark">
    <title>Email Verification - {{APP_NAME}}</title>
    
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:AllowPNG/>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    
    <style>
        /* CSS Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        
        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            outline: none;
            text-decoration: none;
        }
        
        /* Main Body Styles */
        body {
            width: 100% !important;
            height: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background-color: #f8f9fa;
        }
        
        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #1a1a1a !important;
                color: #e1e5e9 !important;
            }
            .email-container {
                background-color: #2c2c2c !important;
                border-color: #404040 !important;
            }
            .header-section {
                background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
            }
            .alternative-section {
                background-color: #333333 !important;
                border-color: #404040 !important;
            }
            .security-notice {
                background-color: #2a2a2a !important;
                border-color: #4a90e2 !important;
            }
        }
        
        /* Container Styles */
        .email-wrapper {
            width: 100%;
            margin: 0;
            padding: 20px 0;
            background-color: #f8f9fa;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
            overflow: hidden;
        }
        
        /* Header Styles */
        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 40px 30px;
        }
        
        .logo-placeholder {
            width: 60px;
            height: 60px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .app-name {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .header-title {
            font-size: 20px;
            font-weight: 400;
            opacity: 0.95;
            margin: 0;
        }
        
        /* Content Styles */
        .content-section {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
        }
        
        .message-text {
            font-size: 16px;
            line-height: 1.7;
            margin-bottom: 20px;
            color: #495057;
        }
        
        /* CTA Button Styles */
        .cta-container {
            text-align: center;
            margin: 35px 0;
        }
        
        .cta-button {
            display: inline-block;
            padding: 16px 32px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            border: none;
            cursor: pointer;
            min-width: 200px;
        }
        
        .cta-button:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
            transform: translateY(-2px);
        }
        
        /* Alternative Link Section */
        .alternative-section {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        
        .alternative-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #495057;
        }
        
        .verification-link {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            background-color: #ffffff;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            word-break: break-all;
            color: #495057;
            line-height: 1.4;
        }
        
        /* Security Notice */
        .security-notice {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .security-title {
            font-size: 16px;
            font-weight: 600;
            color: #1565c0;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        
        .security-icon {
            margin-right: 8px;
        }
        
        .security-text {
            font-size: 14px;
            color: #1976d2;
            line-height: 1.5;
        }
        
        /* Footer Styles */
        .footer-section {
            background-color: #f8f9fa;
            border-top: 1px solid #e9ecef;
            padding: 30px;
            text-align: center;
        }
        
        .footer-text {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 10px;
        }
        
        .footer-links {
            margin: 15px 0;
        }
        
        .footer-link {
            color: #667eea;
            text-decoration: none;
            margin: 0 15px;
            font-size: 14px;
        }
        
        .footer-link:hover {
            text-decoration: underline;
        }
        
        /* Responsive Design */
        @media only screen and (max-width: 600px) {
            .email-wrapper {
                padding: 10px 0 !important;
            }
            
            .email-container {
                margin: 0 10px !important;
                border-radius: 8px !important;
            }
            
            .header-section,
            .content-section,
            .footer-section {
                padding: 25px 20px !important;
            }
            
            .app-name {
                font-size: 24px !important;
            }
            
            .header-title {
                font-size: 18px !important;
            }
            
            .cta-button {
                padding: 14px 24px !important;
                font-size: 15px !important;
                min-width: 180px !important;
            }
            
            .verification-link {
                font-size: 12px !important;
                padding: 10px !important;
            }
        }
        
        @media only screen and (max-width: 480px) {
            .header-section,
            .content-section,
            .footer-section {
                padding: 20px 15px !important;
            }
            
            .alternative-section,
            .security-notice {
                margin: 20px 0 !important;
                padding: 15px !important;
            }
        }
        
        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .email-container {
                border: 2px solid #000000 !important;
            }
            
            .cta-button {
                border: 2px solid #000000 !important;
            }
        }
        
        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .cta-button {
                transition: none !important;
            }
            
            .cta-button:hover {
                transform: none !important;
            }
        }
    </style>
</head>

<body role="article" aria-label="Email verification">
    <div class="email-wrapper">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
            <tr>
                <td>
                    <div class="email-container">
                        <!-- Header Section -->
                        <div class="header-section" role="banner">
                            <div class="logo-placeholder" aria-label="{{APP_NAME}} logo">
                                <!-- Placeholder for logo image -->
                                {{#LOGO_URL}}
                                <img src="{{LOGO_URL}}" alt="{{APP_NAME}} Logo" width="60" height="60" style="border-radius: 12px;">
                                {{/LOGO_URL}}
                                {{^LOGO_URL}}
                                {{APP_NAME_INITIAL}}
                                {{/LOGO_URL}}
                            </div>
                            <h1 class="app-name">{{APP_NAME}}</h1>
                            <p class="header-title">{{VERIFICATION_TITLE}}</p>
                        </div>
                        
                        <!-- Main Content Section -->
                        <div class="content-section" role="main">
                            <p class="greeting">Hello {{USER_EMAIL}},</p>
                            
                            <p class="message-text">{{VERIFICATION_MESSAGE}}</p>
                            
                            <p class="message-text">Click the button below to verify your email address and activate your account:</p>
                            
                            <!-- Call-to-Action Button -->
                            <div class="cta-container">
                                <a href="{{VERIFICATION_URL}}" 
                                   class="cta-button" 
                                   role="button"
                                   aria-label="Verify your email address"
                                   target="_blank"
                                   rel="noopener noreferrer">
                                    ✓ Verify Email Address
                                </a>
                            </div>
                            
                            <!-- Alternative Link Section -->
                            <div class="alternative-section" role="complementary">
                                <p class="alternative-title">
                                    <strong>Can't click the button?</strong>
                                </p>
                                <p style="font-size: 14px; margin-bottom: 10px; color: #495057;">
                                    Copy and paste this link into your browser:
                                </p>
                                <div class="verification-link">{{VERIFICATION_URL}}</div>
                            </div>
                            
                            <!-- Security Notice -->
                            <div class="security-notice" role="note" aria-label="Security information">
                                <p class="security-title">
                                    <span class="security-icon" aria-hidden="true">🔒</span>
                                    Security Notice
                                </p>
                                <p class="security-text">
                                    This verification link will expire in <strong>{{EXPIRES_IN}}</strong>. 
                                    If you didn't create an account with us, you can safely ignore this email. 
                                    Your email address will not be added to our system without verification.
                                </p>
                            </div>
                        </div>
                        
                        <!-- Footer Section -->
                        <div class="footer-section" role="contentinfo">
                            <p class="footer-text">
                                This is an automated message, please do not reply to this email.
                            </p>
                            
                            <div class="footer-links">
                                <a href="{{SUPPORT_URL}}" class="footer-link">Support</a>
                                <a href="{{PRIVACY_URL}}" class="footer-link">Privacy Policy</a>
                                <a href="{{TERMS_URL}}" class="footer-link">Terms of Service</a>
                            </div>
                            
                            <p class="footer-text">
                                &copy; {{CURRENT_YEAR}} {{APP_NAME}}. All rights reserved.
                            </p>
                            
                            {{#COMPANY_ADDRESS}}
                            <p class="footer-text" style="margin-top: 10px;">
                                {{COMPANY_ADDRESS}}
                            </p>
                            {{/COMPANY_ADDRESS}}
                        </div>
                    </div>
                </td>
            </tr>
        </table>
    </div>

    <!-- Fallback Text Version (Hidden) -->
    <div style="display: none; font-size: 1px; color: #ffffff; line-height: 1px; max-height: 0px; max-width: 0px; opacity: 0; overflow: hidden;">
        {{APP_NAME}} - {{VERIFICATION_TITLE}}. Hello {{USER_EMAIL}}, {{VERIFICATION_MESSAGE}} Please verify your email address: {{VERIFICATION_URL}}
    </div>
</body>
</html>

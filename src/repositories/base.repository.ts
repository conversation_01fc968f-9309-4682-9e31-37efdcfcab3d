import { PrismaClient } from '@prisma/client';
import { Service } from 'typedi';

@Service()
export abstract class BaseRepository {
  protected prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  async disconnect() {
    await this.prisma.$disconnect();
  }

  async healthCheck() {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return true;
    } catch {
      return false;
    }
  }
}

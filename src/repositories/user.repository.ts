import { User, <PERSON>rism<PERSON>, <PERSON> } from '@prisma/client';
import { prisma, D<PERSON> } from '../loaders';
import { logger } from '../utils/logger';
import { sanitizeForLog, sanitizeError } from '../utils/logSanitizer';
import { PaginationResult, RepositoryFilters } from '../types/template';
import { BaseRepository } from './base.repository';

export interface CreateUserData {
  email: string;
  username?: string;
  passwordHash?: string | null; // Allow null for OAuth users
  role?: Role;
  name?: string | null;
  image?: string | null;
  emailVerified?: Date | null;
}

export type UpdateUserData = Partial<Omit<User, 'id' | 'createdAt' | 'updatedAt'>>;

export interface UserFilters extends RepositoryFilters {
  email?: string;
  username?: string;
  role?: Role;
  createdAfter?: Date;
  createdBefore?: Date;
}

/**
 * User Repository - Data Access Layer for User operations
 */
export class UserRepository extends BaseRepository {
  /**
   * Create a new user
   */
  static async create(data: CreateUserData): Promise<User> {
    try {
      logger.debug('Creating new user', sanitizeForLog({ email: data.email, username: data.username }));

      const user = await prisma.user.create({
        data: {
          email: data.email,
          username: data.username,
          passwordHash: data.passwordHash,
          role: data.role || Role.USER,
        },
      });

      logger.info('User created successfully', sanitizeForLog({ id: user.id, email: data.email }));
      return user;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          const target = error.meta?.target as string[];
          logger.warn(
            `Attempt to create user with duplicate field`,
            sanitizeForLog({ target, email: data.email, username: data.username }),
          );
          if (target.includes('email')) {
            throw new Error('User with this email already exists');
          }
          if (target.includes('username')) {
            throw new Error('User with this username already exists');
          }
        }
      }
      logger.error('Failed to create user', sanitizeError(error));
      throw error;
    }
  }

  /**
   * Find user by ID
   */
  static async findById(id: string): Promise<User | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { id },
      });

      return user;
    } catch (error) {
      logger.error('Failed to find user by ID', sanitizeError(error));
      throw error;
    }
  }

  /**
   * Find user by email
   */
  static async findByEmail(email: string): Promise<User | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { email },
      });

      return user;
    } catch (error) {
      logger.error('Failed to find user by email', sanitizeError(error));
      throw error;
    }
  }

  /**
   * Find user by username
   */
  static async findByUsername(username: string): Promise<User | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { username },
      });

      return user;
    } catch (error) {
      logger.error('Failed to find user by username', sanitizeError(error));
      throw error;
    }
  }

  /**
   * Find user by verification token.
   * Note: This logic might need to be adapted depending on where the verification token is stored.
   * Assuming it's on the VerificationToken model.
   */
  static async findByVerificationToken(token: string): Promise<User | null> {
    try {
      const verificationToken = await prisma.verificationToken.findUnique({
        where: { token },
      });

      if (!verificationToken) {
        return null;
      }

      return this.findByEmail(verificationToken.identifier);
    } catch (error) {
      logger.error('Failed to find user by verification token', sanitizeError(error));
      throw error;
    }
  }

  /**
   * Update user by ID
   */
  static async update(id: string, data: UpdateUserData): Promise<User> {
    try {
      logger.debug('Updating user', sanitizeForLog({ id }));

      const user = await prisma.user.update({
        where: { id },
        data,
      });

      logger.info('User updated successfully', sanitizeForLog({ id: user.id }));
      return user;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          logger.warn('Attempt to update non-existent user', sanitizeForLog({ id }));
          throw new Error('User not found');
        }
        if (error.code === 'P2002') {
          logger.warn('Attempt to update user with duplicate email or username', sanitizeForLog({ id }));
          throw new Error('User with this email or username already exists');
        }
      }
      logger.error('Failed to update user', sanitizeError(error));
      throw error;
    }
  }

  /**
   * Delete user by ID
   */
  static async delete(id: string): Promise<User> {
    try {
      logger.debug('Deleting user', sanitizeForLog({ id }));

      const user = await prisma.user.delete({
        where: { id },
      });

      logger.info('User deleted successfully', sanitizeForLog({ id }));
      return user;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          logger.warn('Attempt to delete non-existent user', sanitizeForLog({ id }));
          throw new Error('User not found');
        }
      }
      logger.error('Failed to delete user', sanitizeError(error));
      throw error;
    }
  }

  /**
   * Find users with filters and pagination
   */
  static async findMany(
    filters: UserFilters = {},
    page: number = 1,
    limit: number = 10,
  ): Promise<PaginationResult<User>> {
    try {
      const where: Prisma.UserWhereInput = {};

      // Apply filters
      if (filters.email) {
        where.email = {
          contains: filters.email,
          mode: 'insensitive',
        };
      }

      if (filters.username) {
        where.username = {
          contains: filters.username,
          mode: 'insensitive',
        };
      }

      if (filters.role) {
        where.role = filters.role;
      }

      if (filters.createdAfter || filters.createdBefore) {
        where.createdAt = {};
        if (filters.createdAfter) {
          where.createdAt.gte = filters.createdAfter;
        }
        if (filters.createdBefore) {
          where.createdAt.lte = filters.createdBefore;
        }
      }

      // Use DAL paginate helper
      const result = await DAL.paginate<User, Prisma.UserFindManyArgs>(
        prisma.user,
        {
          where,
          orderBy: { createdAt: 'desc' },
        },
        page,
        limit,
      );

      logger.debug(
        'Users retrieved successfully',
        sanitizeForLog({
          total: result.total,
          page: result.page,
          limit: result.limit,
        }),
      );

      // Transform DAL result to match PaginationResult interface
      return {
        data: result.data,
        pagination: {
          page: result.page,
          limit: result.limit,
          total: result.total,
          pages: result.totalPages,
          hasNext: result.hasNext,
          hasPrev: result.hasPrev,
        },
      };
    } catch (error) {
      logger.error('Failed to find users', sanitizeError(error));
      throw error;
    }
  }

  /**
   * Get user count
   */
  static async count(filters: UserFilters = {}): Promise<number> {
    try {
      const where: Prisma.UserWhereInput = {};

      // Apply filters
      if (filters.email) {
        where.email = {
          contains: filters.email,
          mode: 'insensitive',
        };
      }

      if (filters.username) {
        where.username = {
          contains: filters.username,
          mode: 'insensitive',
        };
      }

      if (filters.role) {
        where.role = filters.role;
      }

      if (filters.createdAfter || filters.createdBefore) {
        where.createdAt = {};
        if (filters.createdAfter) {
          where.createdAt.gte = filters.createdAfter;
        }
        if (filters.createdBefore) {
          where.createdAt.lte = filters.createdBefore;
        }
      }

      const count = await prisma.user.count({ where });
      return count;
    } catch (error) {
      logger.error('Failed to count users', sanitizeError(error));
      throw error;
    }
  }

  /**
   * Create multiple users
   */
  static async createMany(usersData: CreateUserData[]): Promise<{ count: number }> {
    try {
      const result = await prisma.user.createMany({
        data: usersData,
        skipDuplicates: true, // Or handle duplicates as needed
      });

      logger.info('Bulk user creation completed', sanitizeForLog({ count: result.count }));
      return result;
    } catch (error) {
      logger.error('Failed to create multiple users', sanitizeError(error));
      throw error;
    }
  }

  /**
   * Find or create user (upsert)
   */
  static async findOrCreate(email: string, createData: Omit<CreateUserData, 'email'>): Promise<User> {
    try {
      const user = await prisma.user.upsert({
        where: { email },
        update: {}, // No update on find, just return existing user
        create: {
          email,
          ...createData,
        },
      });

      logger.info('User found or created', sanitizeForLog({ id: user.id, email: user.email }));
      return user;
    } catch (error) {
      logger.error('Failed to find or create user', sanitizeError(error));
      throw error;
    }
  }

  /**
   * Get users by role
   */
  static async findByRole(role: Role): Promise<User[]> {
    try {
      const users = await prisma.user.findMany({
        where: { role },
        orderBy: { createdAt: 'desc' },
      });

      return users;
    } catch (error) {
      logger.error('Failed to find users by role', sanitizeError(error));
      throw error;
    }
  }
}

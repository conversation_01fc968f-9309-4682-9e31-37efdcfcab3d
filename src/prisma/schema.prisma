generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(cuid())
  name          String?
  username      String?   @unique
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  passwordHash  String?
  role          Role      @default(USER)
  accounts      Account[]
  sessions      Session[]
  logs          AuditLog[]

  // Login attempt tracking for brute-force prevention
  failedLoginAttempts Int       @default(0)
  lockoutExpires      DateTime?

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String  // "oauth", "email", etc
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token         String? @db.Text
  session_state    String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// Historical Logging for Auditing
model AuditLog {
  id        String      @id @default(cuid())
  userId    String
  user      User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  action    AuditAction
  ipAddress String?
  userAgent String?
  createdAt DateTime    @default(now())

  @@map("audit_logs")
}

enum Role {
  USER
  ADMIN
}

enum AuditAction {
  LOGIN_SUCCESS
  LOGIN_FAILURE
  LOGOUT
  PASSWORD_RESET_REQUEST
  PASSWORD_RESET_SUCCESS
  EMAIL_CHANGE_REQUEST
  EMAIL_CHANGE_SUCCESS
  EMAIL_VERIFIED
  OAUTH_LOGIN_SUCCESS
  OAUTH_LOGIN_FAILURE
}

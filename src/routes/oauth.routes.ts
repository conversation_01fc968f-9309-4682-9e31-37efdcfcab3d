import { Router } from 'express';
import { OAuthController } from '../controllers/oauth.controller';
import authMiddleware from '../middlewares/auth.middleware';
import {
  securityHeaders,
  sanitizeInput,
  deviceFingerprint,
  detectSuspiciousActivity,
} from '../middlewares/security.middleware';
import OAuthSecurityMiddleware, {
  validateProvider,
  generateOAuthState,
  validateOAuthState,
  validateCallbackParams,
  validateProviderParam,
  detectSuspiciousOAuthActivity,
  validateOAuthSession,
} from '../middlewares/oauth.middleware';

const router = Router();

// Apply security headers to all OAuth routes
router.use(securityHeaders);

// Apply input sanitization to all OAuth routes
router.use(sanitizeInput);

// Apply device fingerprinting to all OAuth routes
router.use(deviceFingerprint);

// Apply suspicious activity detection to all OAuth routes
router.use(detectSuspiciousActivity);

// Apply OAuth-specific suspicious activity detection
router.use(detectSuspiciousOAuthActivity);

// Validate OAuth session requirements for all routes
router.use(validateOAuthSession);

/**
 * PUBLIC OAUTH ROUTES (No authentication required)
 */

/**
 * @swagger
 * /auth/oauth/providers:
 *   get:
 *     summary: Get available OAuth providers
 *     tags: [OAuth]
 *     responses:
 *       200:
 *         description: List of available OAuth providers
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     providers:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                           displayName:
 *                             type: string
 *                           authUrl:
 *                             type: string
 *                           enabled:
 *                             type: boolean
 *                     total:
 *                       type: number
 */
router.get('/providers', OAuthController.getAvailableProviders);

/**
 * @swagger
 * /auth/oauth/{provider}:
 *   get:
 *     summary: Initiate OAuth authentication
 *     tags: [OAuth]
 *     parameters:
 *       - in: path
 *         name: provider
 *         required: true
 *         schema:
 *           type: string
 *           enum: [google, facebook, github]
 *         description: OAuth provider name
 *     responses:
 *       302:
 *         description: Redirect to OAuth provider
 *       400:
 *         description: Invalid provider or configuration error
 *       429:
 *         description: Rate limit exceeded
 */
router.get(
  '/:provider',
  validateProviderParam,
  validateProvider,
  OAuthSecurityMiddleware.oauthInitiateRateLimit,
  generateOAuthState,
  OAuthController.initiateAuth,
);

/**
 * @swagger
 * /auth/oauth/{provider}/callback:
 *   get:
 *     summary: Handle OAuth callback
 *     tags: [OAuth]
 *     parameters:
 *       - in: path
 *         name: provider
 *         required: true
 *         schema:
 *           type: string
 *           enum: [google, facebook, github]
 *         description: OAuth provider name
 *       - in: query
 *         name: code
 *         schema:
 *           type: string
 *         description: Authorization code from OAuth provider
 *       - in: query
 *         name: state
 *         schema:
 *           type: string
 *         description: State parameter for CSRF protection
 *       - in: query
 *         name: error
 *         schema:
 *           type: string
 *         description: Error code from OAuth provider
 *     responses:
 *       302:
 *         description: Redirect to success or failure URL
 *       400:
 *         description: Invalid callback parameters
 *       429:
 *         description: Rate limit exceeded
 */
router.get(
  '/:provider/callback',
  validateProviderParam,
  validateProvider,
  OAuthSecurityMiddleware.oauthCallbackRateLimit,
  validateCallbackParams,
  validateOAuthState,
  OAuthController.handleCallback,
);

/**
 * AUTHENTICATED OAUTH ROUTES (Authentication required)
 */

/**
 * @swagger
 * /auth/oauth/accounts:
 *   get:
 *     summary: Get user's linked OAuth accounts
 *     tags: [OAuth]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of linked OAuth accounts
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     accounts:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           provider:
 *                             type: string
 *                           linkedAt:
 *                             type: string
 *                             format: date-time
 *                     enabledProviders:
 *                       type: array
 *                       items:
 *                         type: string
 *       401:
 *         description: Unauthorized
 */
router.get('/accounts', authMiddleware, OAuthController.getLinkedAccounts);

/**
 * @swagger
 * /auth/oauth/link/{provider}:
 *   post:
 *     summary: Link OAuth account to existing user
 *     tags: [OAuth]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: provider
 *         required: true
 *         schema:
 *           type: string
 *           enum: [google, facebook, github]
 *         description: OAuth provider name
 *     responses:
 *       302:
 *         description: Redirect to OAuth provider for linking
 *       400:
 *         description: Invalid provider or account already linked
 *       401:
 *         description: Unauthorized
 *       429:
 *         description: Rate limit exceeded
 */
router.post(
  '/link/:provider',
  authMiddleware,
  validateProviderParam,
  validateProvider,
  OAuthSecurityMiddleware.oauthLinkingRateLimit,
  generateOAuthState,
  OAuthController.linkAccount,
);

/**
 * @swagger
 * /auth/oauth/unlink/{provider}:
 *   delete:
 *     summary: Unlink OAuth account from user
 *     tags: [OAuth]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: provider
 *         required: true
 *         schema:
 *           type: string
 *           enum: [google, facebook, github]
 *         description: OAuth provider name
 *     responses:
 *       200:
 *         description: OAuth account unlinked successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       400:
 *         description: Cannot unlink the only authentication method
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: OAuth account not found
 */
router.delete(
  '/unlink/:provider',
  authMiddleware,
  validateProviderParam,
  validateProvider,
  OAuthSecurityMiddleware.oauthLinkingRateLimit,
  OAuthController.unlinkAccount,
);

export default router;

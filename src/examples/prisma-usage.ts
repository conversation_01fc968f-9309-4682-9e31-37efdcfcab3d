/**
 * Example usage of Prisma setup and Data Access Layer
 *
 * This file demonstrates how to use the Prisma loader and DAL helpers
 * that have been implemented in this step.
 */

import { Role } from '@prisma/client';
import { prisma, connectDatabase, disconnectDatabase, databaseHealthCheck, transaction, batch } from '../loaders';
import { UserRepository, CreateUserData } from '../repositories/user.repository';

/**
 * Example 1: Basic database connection
 */
async function exampleConnection() {
  try {
    // Connect to database
    await connectDatabase();

    // Check database health
    const isHealthy = await databaseHealthCheck();
    console.log('Database health:', isHealthy);

    // Disconnect from database
    await disconnectDatabase();
  } catch (error) {
    console.error('Connection example failed:', error);
  }
}

/**
 * Example 2: Basic CRUD operations using UserRepository
 */
async function exampleCRUD() {
  try {
    // Create a new user
    const newUser = await UserRepository.create({
      email: '<EMAIL>',
      passwordHash: 'hashed_password_123',
      role: Role.USER,
    });
    console.log('Created user:', newUser);

    // Find user by email
    const foundUser = await UserRepository.findByEmail('<EMAIL>');
    console.log('Found user:', foundUser);

    // Update user
    if (foundUser) {
      const updatedUser = await UserRepository.update(foundUser.id, {
        role: Role.ADMIN,
      });
      console.log('Updated user:', updatedUser);
    }

    // Find users with pagination
    const usersPage = await UserRepository.findMany(
      { role: Role.ADMIN },
      1, // page
      10, // limit
    );
    console.log('Users page:', usersPage);

    // Clean up - delete the user
    if (foundUser) {
      await UserRepository.delete(foundUser.id);
      console.log('User deleted');
    }
  } catch (error) {
    console.error('CRUD example failed:', error);
  }
}

/**
 * Example 3: Using transactions
 */
async function exampleTransactions() {
  try {
    const result = await transaction(async tx => {
      // Create multiple users in a transaction
      const user1 = await tx.user.create({
        data: {
          email: '<EMAIL>',
          passwordHash: 'hash1',
          role: Role.USER,
        },
      });

      const user2 = await tx.user.create({
        data: {
          email: '<EMAIL>',
          passwordHash: 'hash2',
          role: Role.USER,
        },
      });

      return { user1, user2 };
    });

    console.log('Transaction result:', result);

    // Clean up
    await UserRepository.delete(result.user1.id);
    await UserRepository.delete(result.user2.id);
  } catch (error) {
    console.error('Transaction example failed:', error);
  }
}

/**
 * Example 4: Batch operations
 */
async function exampleBatchOperations() {
  try {
    // Create multiple users at once
    const usersData: CreateUserData[] = [
      { email: '<EMAIL>', passwordHash: 'hash1' },
      { email: '<EMAIL>', passwordHash: 'hash2' },
      { email: '<EMAIL>', passwordHash: 'hash3' },
    ];

    const batchResult = await UserRepository.createMany(usersData);
    console.log('Batch create result:', batchResult);

    // Find all batch created users
    const batchUsers = await UserRepository.findMany({
      email: 'batch', // partial email match
    });
    console.log('Batch created users:', batchUsers);

    // Clean up using batch delete
    if (batchUsers.data.length > 0) {
      const deletePromises = batchUsers.data.map(user => prisma.user.delete({ where: { id: user.id } }));

      await batch(deletePromises);
      console.log('Batch delete completed');
    }
  } catch (error) {
    console.error('Batch example failed:', error);
  }
}

/**
 * Example 5: Raw SQL queries
 */
async function exampleRawSQL() {
  try {
    // Using raw query
    const result = await prisma.$queryRaw<{ count: bigint }[]>`
      SELECT COUNT(*) as count FROM users WHERE role = ${Role.USER}
    `;
    console.log('User count from raw query:', result);

    // Using raw execute
    const affected = await prisma.$executeRaw`
      UPDATE users SET "updatedAt" = NOW() WHERE role = ${Role.USER}
    `;
    console.log('Rows affected by raw execute:', affected);
  } catch (error) {
    console.error('Raw SQL example failed:', error);
  }
}

/**
 * Main function to run all examples
 */
async function main() {
  console.log('🚀 Running Prisma and DAL examples...\n');

  await exampleConnection();
  console.log('✅ Connection example completed\n');

  await exampleCRUD();
  console.log('✅ CRUD example completed\n');

  await exampleTransactions();
  console.log('✅ Transaction example completed\n');

  await exampleBatchOperations();
  console.log('✅ Batch operations example completed\n');

  await exampleRawSQL();
  console.log('✅ Raw SQL example completed\n');

  console.log('🎉 All examples completed successfully!');
}

// Export for use in other files
export { exampleConnection, exampleCRUD, exampleTransactions, exampleBatchOperations, exampleRawSQL, main };

// Run examples if this file is executed directly
if (require.main === module) {
  main()
    .catch(console.error)
    .finally(() => {
      // Ensure graceful shutdown
      disconnectDatabase().catch(console.error);
    });
}

import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ValidateIf } from 'class-validator';

export class RegisterDto {
  @IsString()
  @IsOptional()
  public name?: string;

  @IsEmail()
  @ValidateIf(o => !o.username || o.email)
  public email?: string;

  @IsString()
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  public password!: string;

  @IsString()
  @ValidateIf(o => !o.email || o.username)
  public username?: string;
}

export class LoginDto {
  @IsString()
  @ValidateIf(o => !o.email)
  public username?: string;

  @IsEmail()
  @ValidateIf(o => !o.username)
  public email?: string;

  @IsString()
  public password!: string;
}

export class ChangePasswordDto {
  @IsString()
  currentPassword!: string;

  @IsString()
  @MinLength(8, { message: 'New password must be at least 8 characters long' })
  newPassword!: string;
}

export class VerifyEmailDto {
  @IsString()
  token!: string;
}

export class ResendVerificationDto {
  @IsOptional()
  @IsEmail()
  @ValidateIf(o => !o.username)
  email?: string;

  @IsOptional()
  @IsString()
  @ValidateIf(o => !o.email)
  username?: string;
}

export class ChangeEmailDto {
  @IsEmail()
  newEmail!: string;
}

import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>te<PERSON>f } from 'class-validator';
import { Role } from '@prisma/client';

export class CreateUserDto {
  @IsEmail()
  @ValidateIf(o => !o.username || o.email)
  public email?: string;

  @IsString()
  @ValidateIf(o => !o.provider)
  public password?: string;

  @IsString()
  @IsOptional()
  public name?: string;

  @IsString()
  @ValidateIf(o => !o.email || o.username)
  public username?: string;

  @IsString()
  @IsOptional()
  public provider?: string;

  @IsString()
  @IsOptional()
  public providerAccountId?: string;
}

export class UpdateUserDto {
  @IsEmail()
  @IsOptional()
  public email?: string;

  @IsString()
  @IsOptional()
  public name?: string;

  @IsString()
  @IsOptional()
  public username?: string;

  @IsString()
  @IsOptional()
  public image?: string;

  @IsEnum(Role)
  @IsOptional()
  public role?: Role;
}

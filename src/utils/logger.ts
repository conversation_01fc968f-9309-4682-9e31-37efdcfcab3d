// DEPRECATED: This file is kept for backward compatibility
// All logging should now use the secure logger implementation

import { logger, stream } from './secureLogger';

// Re-export secure logger as default logger
export { logger, stream };

// Add deprecation warning for development
if (process.env.NODE_ENV === 'development') {
  logger.warn('DEPRECATION WARNING: utils/logger.ts is deprecated. Use utils/secureLogger.ts directly.');
}

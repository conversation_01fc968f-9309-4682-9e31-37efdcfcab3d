import winston from 'winston';
import winstonDaily from 'winston-daily-rotate-file';
import { LOG_DIR, NODE_ENV } from '../config';

const logDir: string = LOG_DIR || 'logs';

// Sensitive data patterns to redact
const SENSITIVE_PATTERNS = [
  // JWT token patterns
  /eyJ[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*/g,
  // Authorization headers
  /Bearer\s+[A-Za-z0-9_-]+/g,
  // Password fields (various formats)
  /"password"\s*:\s*"[^"]*"/g,
  /"passwordHash"\s*:\s*"[^"]*"/g,
  /"currentPassword"\s*:\s*"[^"]*"/g,
  /"newPassword"\s*:\s*"[^"]*"/g,
  // API keys
  /api[_-]?key\s*[:=]\s*["']?[A-Za-z0-9_-]+["']?/gi,
  // Session tokens
  /session[_-]?token\s*[:=]\s*["']?[A-Za-z0-9_-]+["']?/gi,
  // Refresh tokens
  /"refreshToken"\s*:\s*"[^"]*"/g,
];

/**
 * Redact sensitive information from log messages
 */
function redactSensitiveData(message: string): string {
  let sanitized = message;

  SENSITIVE_PATTERNS.forEach(pattern => {
    sanitized = sanitized.replace(pattern, match => {
      if (match.includes('eyJ')) {
        // JWT token
        return '"token":"[REDACTED_JWT_TOKEN]"';
      } else if (match.includes('Bearer')) {
        // Authorization header
        return 'Bearer [REDACTED_TOKEN]';
      } else if (match.includes('password')) {
        // Password field
        return match.replace(/:\s*"[^"]*"/, ': "[REDACTED_PASSWORD]"');
      } else if (match.includes('refreshToken')) {
        // Refresh token
        return '"refreshToken":"[REDACTED_REFRESH_TOKEN]"';
      } else {
        // Generic sensitive data
        return '[REDACTED_SENSITIVE_DATA]';
      }
    });
  });

  return sanitized;
}

/**
 * Custom Winston format for secure logging
 */
const secureLogFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss',
  }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    // Redact sensitive data from message
    const sanitizedMessage = typeof message === 'string' ? redactSensitiveData(message) : message;

    // Redact sensitive data from metadata
    const sanitizedMeta = JSON.stringify(meta, (key, value) => {
      if (typeof value === 'string') {
        return redactSensitiveData(value);
      }
      return value;
    });

    const logEntry: any = {
      timestamp,
      level,
      message: sanitizedMessage,
    };

    if (stack && typeof stack === 'string') {
      logEntry.stack = redactSensitiveData(stack);
    }

    if (Object.keys(meta).length > 0) {
      logEntry.meta = JSON.parse(sanitizedMeta);
    }

    return JSON.stringify(logEntry);
  }),
);

/**
 * Secure logger instance with sensitive data redaction
 */
const secureLogger = winston.createLogger({
  format: secureLogFormat,
  transports: [
    // Combined log (info and above) with daily rotation
    new winstonDaily({
      level: 'info',
      datePattern: 'YYYY-MM-DD',
      dirname: logDir + '/combined',
      filename: `%DATE%.log`,
      maxFiles: 30,
      json: false, // Using custom format
      zippedArchive: true,
    }),
    // Error log setting
    new winstonDaily({
      level: 'error',
      datePattern: 'YYYY-MM-DD',
      dirname: logDir + '/error',
      filename: `%DATE%.log`,
      maxFiles: 30,
      handleExceptions: true,
      json: false, // Using custom format
      zippedArchive: true,
    }),
  ],
});

// Add console transport for development environment with secure formatting
if (NODE_ENV === 'development') {
  secureLogger.add(
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.printf(({ timestamp, level, message, ...meta }) => {
          const sanitizedMessage = typeof message === 'string' ? redactSensitiveData(message) : message;
          const metaString = Object.keys(meta).length > 0 ? ` ${redactSensitiveData(JSON.stringify(meta))}` : '';
          return `${timestamp} ${level}: ${sanitizedMessage}${metaString}`;
        }),
      ),
    }),
  );
}

/**
 * Stream interface for Morgan HTTP logging with secure redaction
 */
const secureStream = {
  write: (message: string) => {
    const sanitizedMessage = redactSensitiveData(message);
    secureLogger.info(sanitizedMessage.substring(0, sanitizedMessage.lastIndexOf('\n')));
  },
};

export { secureLogger as logger, secureStream as stream, redactSensitiveData };

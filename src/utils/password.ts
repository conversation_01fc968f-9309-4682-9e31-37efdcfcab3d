import * as bcrypt from 'bcrypt';
import { BCRYPT_SALT_ROUNDS } from '../config';

/**
 * Password utility class with methods for hashing and comparing passwords
 */
export class PasswordUtils {
  private static readonly saltRounds = BCRYPT_SALT_ROUNDS;

  /**
   * Hash a plain text password
   * @param plainTextPassword - The plain text password to hash
   * @param customSaltRounds - Optional custom salt rounds (defaults to env config)
   * @returns Promise that resolves to the hashed password
   * @throws Error if hashing fails
   */
  static async hash(plainTextPassword: string, customSaltRounds?: number): Promise<string> {
    try {
      if (!plainTextPassword) {
        throw new Error('Password cannot be empty');
      }

      if (plainTextPassword.length < 1) {
        throw new Error('Password cannot be empty');
      }

      const saltRounds = customSaltRounds ?? this.saltRounds;

      // Validate salt rounds
      if (saltRounds < 10 || saltRounds > 15) {
        throw new Error('Salt rounds must be between 10 and 15 for security and performance balance');
      }

      const hashedPassword = await bcrypt.hash(plainTextPassword, saltRounds);
      return hashedPassword;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to hash password: ${error.message}`);
      }
      throw new Error('Failed to hash password: Unknown error');
    }
  }

  /**
   * Compare a plain text password with a hashed password
   * @param plainTextPassword - The plain text password to verify
   * @param hashedPassword - The hashed password to compare against
   * @returns Promise that resolves to true if passwords match, false otherwise
   * @throws Error if comparison fails
   */
  static async compare(plainTextPassword: string, hashedPassword: string): Promise<boolean> {
    try {
      if (!plainTextPassword || !hashedPassword) {
        return false;
      }

      const isMatch = await bcrypt.compare(plainTextPassword, hashedPassword);
      return isMatch;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to compare password: ${error.message}`);
      }
      throw new Error('Failed to compare password: Unknown error');
    }
  }

  /**
   * Generate a salt with the specified number of rounds
   * @param saltRounds - The number of salt rounds (defaults to env config)
   * @returns Promise that resolves to the generated salt
   * @throws Error if salt generation fails
   */
  static async generateSalt(saltRounds?: number): Promise<string> {
    try {
      const rounds = saltRounds ?? this.saltRounds;

      if (rounds < 10 || rounds > 15) {
        throw new Error('Salt rounds must be between 10 and 15 for security and performance balance');
      }

      const salt = await bcrypt.genSalt(rounds);
      return salt;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to generate salt: ${error.message}`);
      }
      throw new Error('Failed to generate salt: Unknown error');
    }
  }

  /**
   * Hash a password using a pre-generated salt
   * @param plainTextPassword - The plain text password to hash
   * @param salt - The salt to use for hashing
   * @returns Promise that resolves to the hashed password
   * @throws Error if hashing fails
   */
  static async hashWithSalt(plainTextPassword: string, salt: string): Promise<string> {
    try {
      if (!plainTextPassword) {
        throw new Error('Password cannot be empty');
      }

      if (!salt) {
        throw new Error('Salt cannot be empty');
      }

      const hashedPassword = await bcrypt.hash(plainTextPassword, salt);
      return hashedPassword;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to hash password with salt: ${error.message}`);
      }
      throw new Error('Failed to hash password with salt: Unknown error');
    }
  }

  /**
   * Validate password strength based on common security criteria
   * @param password - The password to validate
   * @returns Object containing validation result and details
   */
  static validatePasswordStrength(password: string): {
    isValid: boolean;
    score: number;
    feedback: string[];
    requirements: {
      minLength: boolean;
      hasUppercase: boolean;
      hasLowercase: boolean;
      hasNumbers: boolean;
      hasSpecialChars: boolean;
    };
  } {
    const feedback: string[] = [];
    let score = 0;

    // Check minimum length (at least 8 characters)
    const minLength = password.length >= 8;
    if (!minLength) {
      feedback.push('Password must be at least 8 characters long');
    } else {
      score += 1;
    }

    // Check for uppercase letters
    const hasUppercase = /[A-Z]/.test(password);
    if (!hasUppercase) {
      feedback.push('Password must contain at least one uppercase letter');
    } else {
      score += 1;
    }

    // Check for lowercase letters
    const hasLowercase = /[a-z]/.test(password);
    if (!hasLowercase) {
      feedback.push('Password must contain at least one lowercase letter');
    } else {
      score += 1;
    }

    // Check for numbers
    const hasNumbers = /\d/.test(password);
    if (!hasNumbers) {
      feedback.push('Password must contain at least one number');
    } else {
      score += 1;
    }

    // Check for special characters
    const hasSpecialChars = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    if (!hasSpecialChars) {
      feedback.push('Password must contain at least one special character');
    } else {
      score += 1;
    }

    // Additional length bonus
    if (password.length >= 12) {
      score += 1;
    }

    const isValid = score >= 4; // Must meet at least 4 criteria

    return {
      isValid,
      score,
      feedback,
      requirements: {
        minLength,
        hasUppercase,
        hasLowercase,
        hasNumbers,
        hasSpecialChars,
      },
    };
  }

  /**
   * Get the current configured salt rounds
   * @returns The number of salt rounds configured
   */
  static getSaltRounds(): number {
    return this.saltRounds;
  }
}

/**
 * Convenience functions for backward compatibility and simpler usage
 */

/**
 * Hash a plain text password
 * @param plainTextPassword - The plain text password to hash
 * @param saltRounds - Optional custom salt rounds
 * @returns Promise that resolves to the hashed password
 */
export const hashPassword = (plainTextPassword: string, saltRounds?: number): Promise<string> => {
  return PasswordUtils.hash(plainTextPassword, saltRounds);
};

/**
 * Compare a plain text password with a hashed password
 * @param plainTextPassword - The plain text password to verify
 * @param hashedPassword - The hashed password to compare against
 * @returns Promise that resolves to true if passwords match, false otherwise
 */
export const comparePassword = (plainTextPassword: string, hashedPassword: string): Promise<boolean> => {
  return PasswordUtils.compare(plainTextPassword, hashedPassword);
};

/**
 * Validate password strength
 * @param password - The password to validate
 * @returns Object containing validation result and details
 */
export const validatePassword = (password: string) => {
  return PasswordUtils.validatePasswordStrength(password);
};

export default PasswordUtils;

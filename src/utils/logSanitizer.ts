/**
 * Log Sanitization Utility
 *
 * Provides utilities to sanitize objects before logging to prevent sensitive
 * data like tokens, passwords, and other credentials from being logged.
 */

interface SanitizeOptions {
  whitelistedKeys?: string[];
  maxDepth?: number;
}

/**
 * Sanitizes an object for logging by:
 * 1. Removing all keys that contain 'token', 'password', 'hash', 'secret', or 'key'
 * 2. Only allowing explicitly whitelisted keys when provided
 * 3. Recursively sanitizing nested objects (up to maxDepth)
 */
export function sanitizeForLog(obj: unknown, options: SanitizeOptions = {}): unknown {
  const { whitelistedKeys = [], maxDepth = 3 } = options;

  return sanitizeRecursive(obj, whitelistedKeys, maxDepth, 0);
}

function sanitizeRecursive(obj: unknown, whitelistedKeys: string[], maxDepth: number, currentDepth: number): unknown {
  // Return primitive values as-is
  if (obj === null || obj === undefined || typeof obj !== 'object') {
    return obj;
  }

  // Prevent infinite recursion
  if (currentDepth >= maxDepth) {
    return '[Object - max depth reached]';
  }

  // Handle arrays
  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeRecursive(item, whitelistedKeys, maxDepth, currentDepth + 1));
  }

  // Handle objects
  const sanitized: Record<string, unknown> = {};
  const entries = Object.entries(obj as Record<string, unknown>);

  for (const [key, value] of entries) {
    const lowerKey = key.toLowerCase();

    // Check if key contains sensitive data patterns
    const isSensitive = [
      'token',
      'password',
      'hash',
      'secret',
      'key',
      'auth',
      'credential',
      'cookie',
      'session',
      'bearer',
    ].some(pattern => lowerKey.includes(pattern));

    if (isSensitive) {
      // Replace sensitive data with redacted placeholder
      sanitized[key] = '[REDACTED]';
    } else if (whitelistedKeys.length > 0 && !whitelistedKeys.includes(key)) {
      // If whitelist is provided, only include whitelisted keys
      continue;
    } else {
      // Recursively sanitize nested objects
      sanitized[key] = sanitizeRecursive(value, whitelistedKeys, maxDepth, currentDepth + 1);
    }
  }

  return sanitized;
}

/**
 * Sanitize AuthResponse objects for logging
 * Only logs safe user data and metadata, completely removes tokens
 */
export function sanitizeAuthResponse(authResponse: any): any {
  if (!authResponse || typeof authResponse !== 'object') {
    return authResponse;
  }

  return sanitizeForLog(authResponse, {
    whitelistedKeys: ['user', 'expiresIn', 'tokenType'],
  });
}

/**
 * Sanitize User objects for logging
 * Only logs safe user information, removes sensitive data
 */
export function sanitizeUser(user: any): any {
  if (!user || typeof user !== 'object') {
    return user;
  }

  return sanitizeForLog(user, {
    whitelistedKeys: ['id', 'email', 'role', 'createdAt', 'updatedAt'],
  });
}

/**
 * Sanitize AuthEvent objects for logging
 * Includes user identification and metadata but excludes sensitive context
 */
export function sanitizeAuthEvent(authEvent: any): any {
  if (!authEvent || typeof authEvent !== 'object') {
    return authEvent;
  }

  return sanitizeForLog(authEvent, {
    whitelistedKeys: ['type', 'timestamp', 'userId', 'email', 'metadata'],
  });
}

/**
 * Sanitize generic error objects for logging
 * Removes sensitive data while preserving useful debugging information
 */
export function sanitizeError(error: any): any {
  if (!error) {
    return error;
  }

  if (error instanceof Error) {
    return {
      name: error.name,
      message: sanitizeString(error.message),
      stack: error.stack ? sanitizeString(error.stack) : undefined,
    };
  }

  return sanitizeForLog(error, {
    whitelistedKeys: ['name', 'message', 'code', 'status', 'statusCode'],
  });
}

/**
 * Sanitize strings by removing potential sensitive patterns
 */
function sanitizeString(str: string): string {
  if (typeof str !== 'string') {
    return str;
  }

  // Remove JWT tokens
  let sanitized = str.replace(/eyJ[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*/g, '[REDACTED_JWT_TOKEN]');

  // Remove Bearer tokens
  sanitized = sanitized.replace(/Bearer\s+[A-Za-z0-9_-]+/g, 'Bearer [REDACTED_TOKEN]');

  // Remove password-like patterns in strings
  sanitized = sanitized.replace(/password["\s]*[:=]["\s]*[^"\s,}]+/gi, 'password: "[REDACTED_PASSWORD]"');

  return sanitized;
}

/**
 * Create a logger metadata object with sanitized data
 */
export function createLogMeta(data: Record<string, unknown>, whitelistedKeys?: string[]): Record<string, unknown> {
  const options: SanitizeOptions = {};
  if (whitelistedKeys !== undefined) {
    options.whitelistedKeys = whitelistedKeys;
  }
  return sanitizeForLog(data, options) as Record<string, unknown>;
}

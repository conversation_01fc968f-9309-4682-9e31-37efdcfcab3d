import { User, Role } from '@prisma/client';
import { UserRepository } from '../repositories/user.repository';
import { HttpException } from '../exceptions/HttpException';
import { logger } from '../utils/secureLogger';
import { sanitizeError, sanitizeAuthEvent } from '../utils/logSanitizer';
import { JwtUtils } from '../utils/jwt';
import { AuthEvent } from '../types/template';
import { AuthContext } from './auth.service';
import { OAuthProvider } from '../config/oauth.config';
import { AuthService } from './auth.service';
import { prisma } from '../loaders/prisma';

/**
 * OAuth Profile Interface
 * Standardized profile structure from different OAuth providers
 */
export interface OAuthProfile {
  id: string;
  provider: OAuthProvider;
  email?: string;
  name?: string;
  picture?: string;
  username?: string;
  verified?: boolean;
}

/**
 * OAuth Authentication Response
 */
export interface OAuthAuthResponse {
  user: Partial<User>;
  token: string;
  refreshToken: string;
  expiresIn: string;
  tokenType: string;
  isNewUser: boolean;
  linkedAccount?: boolean;
}

/**
 * Account Linking Request
 */
export interface AccountLinkingRequest {
  userId: string;
  provider: OAuthProvider;
  providerAccountId: string;
  accessToken: string;
  refreshToken?: string;
  profile: OAuthProfile;
}

/**
 * OAuth Service
 * Handles OAuth authentication, account creation, and account linking
 * Integrates with existing security infrastructure
 */
export class OAuthService {
  /**
   * Handle OAuth authentication callback
   * Creates new user or links to existing account
   */
  static async handleOAuthCallback(
    profile: OAuthProfile,
    accessToken: string,
    refreshToken: string | undefined,
    context?: AuthContext,
  ): Promise<OAuthAuthResponse> {
    try {
      // Check if account already exists
      const existingAccount = await this.findExistingAccount(profile.provider, profile.id);

      if (existingAccount) {
        // User already has this OAuth account linked
        return await this.authenticateExistingOAuthUser(existingAccount, profile, accessToken, refreshToken, context);
      }

      // Check if user exists with same email
      const existingUser = profile.email ? await UserRepository.findByEmail(profile.email) : null;

      if (existingUser) {
        // Link OAuth account to existing user
        return await this.linkOAuthAccountToExistingUser(existingUser, profile, accessToken, refreshToken, context);
      }

      // Create new user with OAuth account
      return await this.createNewOAuthUser(profile, accessToken, refreshToken, context);
    } catch (error) {
      logger.error('OAuth callback handling failed', {
        provider: profile.provider,
        profileId: profile.id,
        email: profile.email,
        error: sanitizeError(error),
      });
      throw error;
    }
  }

  /**
   * Find existing OAuth account
   */
  private static async findExistingAccount(provider: OAuthProvider, providerAccountId: string) {
    return await prisma.account.findUnique({
      where: {
        provider_providerAccountId: {
          provider,
          providerAccountId,
        },
      },
      include: {
        user: true,
      },
    });
  }

  /**
   * Authenticate existing OAuth user
   */
  private static async authenticateExistingOAuthUser(
    account: any,
    profile: OAuthProfile,
    accessToken: string,
    refreshToken: string | undefined,
    context?: AuthContext,
  ): Promise<OAuthAuthResponse> {
    const user = account.user;

    // Update OAuth tokens
    await this.updateOAuthTokens(account.id, accessToken, refreshToken);

    // Update user profile if needed
    await this.syncUserProfile(user, profile);

    // Generate JWT tokens
    const token = await JwtUtils.sign({
      id: user.id,
      email: user.email!,
      role: user.role,
    });
    const jwtRefreshToken = await JwtUtils.sign(
      {
        id: user.id,
        email: user.email!,
        role: user.role,
      },
      { expiresIn: '7d' },
    );

    // Note: Session versioning initialization would need a request object
    // This is handled in the controller after successful OAuth

    // Log authentication event
    const authEvent: AuthEvent = {
      type: 'AUTH_OAUTH_LOGIN',
      timestamp: new Date(),
      userId: user.id,
      email: user.email!,
      metadata: {
        provider: profile.provider,
        ip: context?.ip,
        userAgent: context?.userAgent,
        deviceFingerprint: context?.deviceFingerprint,
      },
    };
    logger.info('OAuth user authenticated successfully', sanitizeAuthEvent(authEvent));

    return {
      user: (AuthService as any).sanitizeUser(user),
      token,
      refreshToken: jwtRefreshToken,
      expiresIn: '7d',
      tokenType: 'Bearer',
      isNewUser: false,
    };
  }

  /**
   * Link OAuth account to existing user
   */
  private static async linkOAuthAccountToExistingUser(
    user: User,
    profile: OAuthProfile,
    accessToken: string,
    refreshToken: string | undefined,
    context?: AuthContext,
  ): Promise<OAuthAuthResponse> {
    // Create OAuth account link
    await this.createOAuthAccount({
      userId: user.id,
      provider: profile.provider,
      providerAccountId: profile.id,
      accessToken,
      refreshToken,
      profile,
    });

    // Update user profile if needed
    await this.syncUserProfile(user, profile);

    // Generate JWT tokens
    const token = await JwtUtils.sign({
      id: user.id,
      email: user.email!,
      role: user.role,
    });
    const jwtRefreshToken = await JwtUtils.sign(
      {
        id: user.id,
        email: user.email!,
        role: user.role,
      },
      { expiresIn: '7d' },
    );

    // Note: Session versioning initialization would need a request object
    // This is handled in the controller after successful OAuth

    // Log account linking event
    const authEvent: AuthEvent = {
      type: 'AUTH_LOGIN',
      timestamp: new Date(),
      userId: user.id,
      email: user.email!,
      metadata: {
        provider: profile.provider,
        linkedToExisting: true,
        ip: context?.ip,
        userAgent: context?.userAgent,
        deviceFingerprint: context?.deviceFingerprint,
      },
    };
    logger.info('OAuth account linked to existing user', sanitizeAuthEvent(authEvent));

    return {
      user: { ...user, passwordHash: undefined } as Omit<User, 'passwordHash'>,
      token,
      refreshToken: jwtRefreshToken,
      expiresIn: '7d',
      tokenType: 'Bearer',
      isNewUser: false,
      linkedAccount: true,
    };
  }

  /**
   * Create new user with OAuth account
   */
  private static async createNewOAuthUser(
    profile: OAuthProfile,
    accessToken: string,
    refreshToken: string | undefined,
    context?: AuthContext,
  ): Promise<OAuthAuthResponse> {
    // Create user
    // Ensure email is provided for OAuth users
    if (!profile.email) {
      throw new HttpException(400, 'Email is required for OAuth registration');
    }

    const userData = {
      email: profile.email,
      name: profile.name,
      username: profile.username,
      image: profile.picture,
      emailVerified: profile.verified ? new Date() : null,
      role: Role.USER,
      passwordHash: null, // OAuth users don't have passwords
    };

    const user = await UserRepository.create(userData);

    // Create OAuth account link
    await this.createOAuthAccount({
      userId: user.id,
      provider: profile.provider,
      providerAccountId: profile.id,
      accessToken,
      refreshToken,
      profile,
    });

    // Generate JWT tokens
    const token = await JwtUtils.sign({
      id: user.id,
      email: user.email!,
      role: user.role,
    });
    const jwtRefreshToken = await JwtUtils.sign(
      {
        id: user.id,
        email: user.email!,
        role: user.role,
      },
      { expiresIn: '7d' },
    );

    // Initialize session versioning
    // Note: Session versioning initialization would need a request object
    // This is handled in the controller after successful OAuth

    // Log user creation event
    const authEvent: AuthEvent = {
      type: 'AUTH_REGISTER',
      timestamp: new Date(),
      userId: user.id,
      email: user.email!,
      metadata: {
        provider: profile.provider,
        newUser: true,
        ip: context?.ip,
        userAgent: context?.userAgent,
        deviceFingerprint: context?.deviceFingerprint,
      },
    };
    logger.info('New OAuth user created successfully', sanitizeAuthEvent(authEvent));

    return {
      user: { ...user, passwordHash: undefined } as Omit<User, 'passwordHash'>,
      token,
      refreshToken: jwtRefreshToken,
      expiresIn: '7d',
      tokenType: 'Bearer',
      isNewUser: true,
    };
  }

  /**
   * Create OAuth account record
   */
  private static async createOAuthAccount(request: AccountLinkingRequest): Promise<void> {
    await prisma.account.create({
      data: {
        userId: request.userId,
        type: 'oauth',
        provider: request.provider,
        providerAccountId: request.providerAccountId,
        access_token: request.accessToken,
        refresh_token: request.refreshToken,
        expires_at: null, // Will be handled by provider-specific logic
        token_type: 'Bearer',
        scope: this.getProviderScopes(request.provider).join(' '),
      },
    });
  }

  /**
   * Update OAuth tokens
   */
  private static async updateOAuthTokens(accountId: string, accessToken: string, refreshToken?: string): Promise<void> {
    const updateData: any = {
      access_token: accessToken,
    };

    if (refreshToken) {
      updateData.refresh_token = refreshToken;
    }

    await prisma.account.update({
      where: { id: accountId },
      data: updateData,
    });
  }

  /**
   * Sync user profile with OAuth data
   */
  private static async syncUserProfile(user: User, profile: OAuthProfile): Promise<void> {
    const updates: Partial<User> = {};

    // Update name if not set
    if (!user.name && profile.name) {
      updates.name = profile.name;
    }

    // Update image if not set
    if (!user.image && profile.picture) {
      updates.image = profile.picture;
    }

    // Update email verification if OAuth provider verified it
    if (!user.emailVerified && profile.verified && profile.email === user.email) {
      updates.emailVerified = new Date();
    }

    if (Object.keys(updates).length > 0) {
      await UserRepository.update(user.id, updates);
    }
  }

  /**
   * Get provider-specific scopes
   */
  private static getProviderScopes(provider: OAuthProvider): string[] {
    switch (provider) {
      case 'google':
        return ['profile', 'email'];
      case 'facebook':
        return ['email', 'public_profile'];
      case 'github':
        return ['user:email'];
      default:
        return [];
    }
  }

  /**
   * Unlink OAuth account
   */
  static async unlinkOAuthAccount(userId: string, provider: OAuthProvider): Promise<void> {
    try {
      // Check if user has password or other OAuth accounts
      const user = await UserRepository.findById(userId);
      if (!user) {
        throw new HttpException(404, 'User not found');
      }

      const accounts = await prisma.account.findMany({
        where: { userId },
      });

      // Prevent unlinking if it's the only authentication method
      if (accounts.length === 1 && !user.passwordHash) {
        throw new HttpException(400, 'Cannot unlink the only authentication method. Please set a password first.');
      }

      // Remove the OAuth account
      await prisma.account.deleteMany({
        where: {
          userId,
          provider,
        },
      });

      logger.info('OAuth account unlinked successfully', {
        userId,
        provider,
      });
    } catch (error) {
      logger.error('Failed to unlink OAuth account', {
        userId,
        provider,
        error: sanitizeError(error),
      });
      throw error;
    }
  }

  /**
   * Get user's linked OAuth accounts
   */
  static async getUserOAuthAccounts(userId: string) {
    return await prisma.account.findMany({
      where: {
        userId,
        type: 'oauth',
      },
      select: {
        id: true,
        provider: true,
        providerAccountId: true,
      },
    });
  }

  /**
   * Find user by ID (helper method)
   */
  static async findUserById(userId: string): Promise<User | null> {
    return await UserRepository.findById(userId);
  }
}

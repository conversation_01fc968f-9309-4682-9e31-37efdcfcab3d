import { Service } from 'typedi';
import { logger } from '../utils/logger';
import { sanitizeForLog, sanitizeError } from '../utils/logSanitizer';

@Service()
export abstract class BaseService {
  protected logger = logger;

  protected logInfo(message: string, meta?: any) {
    this.logger.info(message, meta ? sanitizeForLog(meta) : undefined);
  }

  protected logError(message: string, error?: any) {
    this.logger.error(message, error ? sanitizeError(error) : undefined);
  }

  protected logWarn(message: string, meta?: any) {
    this.logger.warn(message, meta ? sanitizeForLog(meta) : undefined);
  }
}

import * as crypto from 'crypto';
import { User } from '@prisma/client';
import { prisma } from '../loaders';
import { Service } from 'typedi';
import { UserRepository } from '../repositories/user.repository';
import { EmailService } from './email.service';
import { HttpException } from '../exceptions/HttpException';
import { sanitizeError, sanitizeAuthEvent } from '../utils/logSanitizer';
import { BaseService } from './base.service';
import { AuthEvent } from '../types/template';
import { env } from '../config';

// Add global declarations for process and setInterval
declare const process: any;
declare const setInterval: any;

export interface EmailVerificationContext {
  ip?: string | undefined;
  userAgent?: string | undefined;
  deviceFingerprint?: string | undefined;
}

export interface EmailVerificationResult {
  success: boolean;
  message: string;
  user?: Omit<User, 'passwordHash'>;
}

export interface EmailVerificationSendResult {
  success: boolean;
  message: string;
  canResendAfter?: Date;
}

/**
 * Rate limiting for email verification resend requests
 * In-memory storage for simplicity - use Redis for production scaling
 */
class EmailVerificationRateLimit {
  private static resendAttempts = new Map<string, { count: number; lastAttempt: Date; nextAllowed: Date }>();
  private static readonly MAX_RESENDS_PER_HOUR = 3;
  private static readonly RESEND_COOLDOWN_MINUTES = 5;
  private static readonly CLEANUP_INTERVAL_MS = 60 * 60 * 1000; // 1 hour

  static {
    // Periodic cleanup of old entries - skip in test environment
    if (process.env.NODE_ENV !== 'test') {
      setInterval(() => {
        const cutoff = new Date(Date.now() - 2 * 60 * 60 * 1000); // 2 hours ago
        Array.from(this.resendAttempts.entries()).forEach(([key, data]) => {
          if (data.lastAttempt < cutoff) {
            this.resendAttempts.delete(key);
          }
        });
      }, this.CLEANUP_INTERVAL_MS);
    }
  }

  static canResend(userId: string): { canResend: boolean; nextAllowed?: Date; attemptsLeft?: number } {
    const key = `resend:${userId}`;
    const now = new Date();
    const data = this.resendAttempts.get(key);

    if (!data) {
      return { canResend: true, attemptsLeft: this.MAX_RESENDS_PER_HOUR };
    }

    // Check if cooldown period has passed
    if (now >= data.nextAllowed) {
      // Reset if it's been an hour since first attempt
      const hourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      if (data.lastAttempt <= hourAgo) {
        this.resendAttempts.delete(key);
        return { canResend: true, attemptsLeft: this.MAX_RESENDS_PER_HOUR };
      }
      return { canResend: true, attemptsLeft: Math.max(0, this.MAX_RESENDS_PER_HOUR - data.count) };
    }

    return {
      canResend: false,
      nextAllowed: data.nextAllowed,
      attemptsLeft: Math.max(0, this.MAX_RESENDS_PER_HOUR - data.count),
    };
  }

  static recordResendAttempt(userId: string): void {
    const key = `resend:${userId}`;
    const now = new Date();
    const data = this.resendAttempts.get(key);

    if (!data) {
      this.resendAttempts.set(key, {
        count: 1,
        lastAttempt: now,
        nextAllowed: new Date(now.getTime() + this.RESEND_COOLDOWN_MINUTES * 60 * 1000),
      });
    } else {
      this.resendAttempts.set(key, {
        count: data.count + 1,
        lastAttempt: now,
        nextAllowed: new Date(now.getTime() + this.RESEND_COOLDOWN_MINUTES * 60 * 1000),
      });
    }
  }

  static getRemainingCooldown(userId: string): number {
    const key = `resend:${userId}`;
    const data = this.resendAttempts.get(key);

    if (!data) return 0;

    const now = new Date();
    const remaining = data.nextAllowed.getTime() - now.getTime();
    return Math.max(0, Math.ceil(remaining / 1000)); // Return seconds
  }
}

@Service()
export class EmailVerificationService extends BaseService {
  constructor(private emailService: EmailService = new EmailService()) {
    super();
  }

  /**
   * Generate a cryptographically secure email verification token
   */
  private generateVerificationToken(): string {
    // Generate 32 random bytes and convert to hex (64 characters)
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Hash a verification token for secure storage
   */
  private hashToken(token: string): string {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  /**
   * Calculate token expiration time
   */
  private calculateTokenExpiry(): Date {
    // Parse the expiry time from config (e.g., "48h", "2d", "7200s")
    const expiryStr = env.EMAIL_VERIFICATION_TOKEN_EXPIRES;
    let expiryMs: number;

    if (expiryStr.endsWith('h')) {
      expiryMs = parseInt(expiryStr) * 60 * 60 * 1000; // hours to milliseconds
    } else if (expiryStr.endsWith('d')) {
      expiryMs = parseInt(expiryStr) * 24 * 60 * 60 * 1000; // days to milliseconds
    } else if (expiryStr.endsWith('s')) {
      expiryMs = parseInt(expiryStr) * 1000; // seconds to milliseconds
    } else if (expiryStr.endsWith('m')) {
      expiryMs = parseInt(expiryStr) * 60 * 1000; // minutes to milliseconds
    } else {
      // Default to milliseconds if no unit specified
      expiryMs = parseInt(expiryStr) || 48 * 60 * 60 * 1000; // default 48 hours
    }

    return new Date(Date.now() + expiryMs);
  }

  /**
   * Create and send email verification for a new user (convenience method)
   */
  async createAndSend(user: User, context?: EmailVerificationContext): Promise<EmailVerificationSendResult> {
    return this.sendEmailVerification(user.id, context);
  }

  /**
   * Send email verification (for new users)
   */
  async sendEmailVerification(
    userId: string,
    context?: EmailVerificationContext,
  ): Promise<EmailVerificationSendResult> {
    try {
      // Check rate limiting
      const rateLimitCheck = EmailVerificationRateLimit.canResend(userId);
      if (!rateLimitCheck.canResend) {
        const cooldownSeconds = EmailVerificationRateLimit.getRemainingCooldown(userId);
        const canResendAfter = new Date(Date.now() + cooldownSeconds * 1000);

        this.logWarn('Email verification resend rate limited', {
          userId,
          cooldownSeconds,
          attemptsLeft: rateLimitCheck.attemptsLeft,
          ip: context?.ip,
        });

        return {
          success: false,
          message: `Please wait ${Math.ceil(cooldownSeconds / 60)} minutes before requesting another verification email.`,
          canResendAfter,
        };
      }

      // Find the user
      const user = await UserRepository.findById(userId);
      if (!user) {
        throw new HttpException(404, 'User not found');
      }

      // Check if already verified
      if (user.emailVerified) {
        this.logInfo('Attempt to send verification to already verified email', {
          userId,
          email: user.email,
          ip: context?.ip,
        });
        return {
          success: false,
          message: 'Email is already verified',
        };
      }

      // Generate new verification token
      const token = this.generateVerificationToken();
      const tokenHash = this.hashToken(token);
      const expiresAt = this.calculateTokenExpiry();

      // Update user with new verification token
      await prisma.verificationToken.create({
        data: {
          identifier: user.email!,
          token: tokenHash,
          expires: expiresAt,
        },
      });

      // Send verification email
      const emailResult = await this.emailService.sendVerificationEmail(
        user as any,
        token,
        false, // isEmailChange = false for new user verification
      );

      if (!emailResult.success) {
        this.logError('Failed to send verification email', {
          userId,
          email: user.email,
          error: emailResult.error,
          ip: context?.ip,
        });
        throw new HttpException(500, 'Failed to send verification email');
      }

      // Record rate limit attempt
      EmailVerificationRateLimit.recordResendAttempt(userId);

      // Audit log
      const authEvent: AuthEvent = {
        type: 'AUTH_REGISTER' as any, // Extend AuthEvent type to include email verification events
        timestamp: new Date(),
        userId: user.id,
        email: user.email!,
        metadata: {
          action: 'email_verification_sent',
          ip: context?.ip,
          userAgent: context?.userAgent,
          deviceFingerprint: context?.deviceFingerprint,
          messageId: emailResult.messageId,
          expiresAt: expiresAt.toISOString(),
        },
      };
      this.logInfo('Email verification sent successfully', sanitizeAuthEvent(authEvent));

      return {
        success: true,
        message: 'Verification email sent successfully',
      };
    } catch (error) {
      this.logError('Failed to send email verification', {
        userId,
        error: sanitizeError(error),
        ip: context?.ip,
      });
      throw error;
    }
  }

  /**
   * Send email change verification (for email updates)
   */
  async sendEmailChangeVerification(
    userId: string,
    newEmail: string,
    context?: EmailVerificationContext,
  ): Promise<EmailVerificationSendResult> {
    try {
      // Check rate limiting
      const rateLimitCheck = EmailVerificationRateLimit.canResend(userId);
      if (!rateLimitCheck.canResend) {
        const cooldownSeconds = EmailVerificationRateLimit.getRemainingCooldown(userId);
        const canResendAfter = new Date(Date.now() + cooldownSeconds * 1000);

        this.logWarn('Email change verification resend rate limited', {
          userId,
          newEmail,
          cooldownSeconds,
          ip: context?.ip,
        });

        return {
          success: false,
          message: `Please wait ${Math.ceil(cooldownSeconds / 60)} minutes before requesting another verification email.`,
          canResendAfter,
        };
      }

      // Find the user
      const user = await UserRepository.findById(userId);
      if (!user) {
        throw new HttpException(404, 'User not found');
      }

      // Check if new email is already taken by another user
      const existingUser = await UserRepository.findByEmail(newEmail);
      if (existingUser && existingUser.id !== userId) {
        throw new HttpException(409, 'Email is already in use by another account');
      }

      // Generate new verification token
      const token = this.generateVerificationToken();
      const tokenHash = this.hashToken(token);
      const expiresAt = this.calculateTokenExpiry();

      // Update user with new verification token and pending email
      // We don't store pendingEmail on the user anymore.
      // The verification token will be associated with the new email.
      await prisma.verificationToken.create({
        data: {
          identifier: newEmail,
          token: tokenHash,
          expires: expiresAt,
        },
      });

      // Send verification email to NEW email address
      const emailResult = await this.emailService.sendVerificationEmail(
        { ...user, email: newEmail } as any,
        token,
        true, // isEmailChange = true
      );

      if (!emailResult.success) {
        this.logError('Failed to send email change verification', {
          userId,
          currentEmail: user.email,
          newEmail,
          error: emailResult.error,
          ip: context?.ip,
        });
        throw new HttpException(500, 'Failed to send verification email');
      }

      // Record rate limit attempt
      EmailVerificationRateLimit.recordResendAttempt(userId);

      // Audit log
      const authEvent: AuthEvent = {
        type: 'AUTH_REGISTER' as any,
        timestamp: new Date(),
        userId: user.id,
        email: user.email!,
        metadata: {
          action: 'email_change_verification_sent',
          currentEmail: user.email!,
          newEmail: newEmail,
          ip: context?.ip,
          userAgent: context?.userAgent,
          deviceFingerprint: context?.deviceFingerprint,
          messageId: emailResult.messageId,
          expiresAt: expiresAt.toISOString(),
        },
      };
      this.logInfo('Email change verification sent successfully', sanitizeAuthEvent(authEvent));

      return {
        success: true,
        message: 'Verification email sent to your new email address',
      };
    } catch (error) {
      this.logError('Failed to send email change verification', {
        userId,
        newEmail,
        error: sanitizeError(error),
        ip: context?.ip,
      });
      throw error;
    }
  }

  /**
   * Verify email using token (handles both new user verification and email change)
   */
  async verifyEmail(token: string, context?: EmailVerificationContext): Promise<EmailVerificationResult> {
    try {
      if (!token) {
        // Log failed verification attempt with security event
        const failedEvent: AuthEvent = {
          type: 'EMAIL_VERIFICATION_FAILED',
          timestamp: new Date(),
          userId: 'unknown',
          email: 'unknown',
          metadata: {
            reason: 'missing_token',
            ip: context?.ip,
            userAgent: context?.userAgent,
          },
        };
        this.logWarn('Email verification failed - missing token', sanitizeAuthEvent(failedEvent));
        throw new HttpException(400, 'Verification token is required');
      }

      // Hash the provided token to compare with stored hash
      const tokenHash = this.hashToken(token);

      // Find user by token hash
      const targetUser = await UserRepository.findByVerificationToken(tokenHash);

      if (!targetUser) {
        // Log failed verification attempt with security event
        const failedEvent: AuthEvent = {
          type: 'EMAIL_VERIFICATION_FAILED',
          timestamp: new Date(),
          userId: 'unknown',
          email: 'unknown',
          metadata: {
            reason: 'invalid_token',
            tokenPreview: token.substring(0, 8) + '...',
            ip: context?.ip,
            userAgent: context?.userAgent,
          },
        };
        this.logWarn('Email verification failed - invalid token', sanitizeAuthEvent(failedEvent));
        throw new HttpException(400, 'Invalid or expired verification token');
      }

      // Check if token has expired
      const verificationToken = await prisma.verificationToken.findUnique({
        where: { token: tokenHash },
      });

      if (!verificationToken || new Date() > verificationToken.expires) {
        // Log failed verification attempt with security event
        const failedEvent: AuthEvent = {
          type: 'EMAIL_VERIFICATION_FAILED',
          timestamp: new Date(),
          userId: targetUser.id,
          email: targetUser.email!,
          metadata: {
            reason: 'expired_token',
            expiredAt: verificationToken?.expires?.toISOString(),
            ip: context?.ip,
            userAgent: context?.userAgent,
          },
        };
        this.logWarn('Email verification failed - expired token', sanitizeAuthEvent(failedEvent));
        throw new HttpException(400, 'Verification token has expired');
      }

      // Check if user is already verified (for initial verification tokens)
      if (targetUser.emailVerified) {
        // Log failed verification attempt with security event
        const failedEvent: AuthEvent = {
          type: 'EMAIL_VERIFICATION_FAILED',
          timestamp: new Date(),
          userId: targetUser.id,
          email: targetUser.email!,
          metadata: {
            reason: 'already_verified',
            ip: context?.ip,
            userAgent: context?.userAgent,
          },
        };
        this.logWarn('Email verification failed - email already verified', sanitizeAuthEvent(failedEvent));
        throw new HttpException(400, 'Email is already verified');
      }

      const updateData: any = {};
      let actionType = 'email_verified';
      let resultMessage = 'Email verified successfully';
      const isEmailChange = verificationToken.identifier !== targetUser.email;

      if (isEmailChange) {
        // This is an email change verification
        updateData.email = verificationToken.identifier;
        updateData.emailVerified = new Date();
        actionType = 'email_change_verified';
        resultMessage = 'Email address updated successfully';

        this.logInfo('Email change verification completed', {
          userId: targetUser.id,
          oldEmail: targetUser.email,
          newEmail: verificationToken.identifier,
          ip: context?.ip,
        });
      } else {
        // This is initial email verification
        updateData.emailVerified = new Date();
        actionType = 'email_verified';
        resultMessage = 'Email verified successfully';

        this.logInfo('Initial email verification completed', {
          userId: targetUser.id,
          email: targetUser.email,
          ip: context?.ip,
        });
      }

      // Update the user
      const updatedUser = await UserRepository.update(targetUser.id, updateData);

      // Delete the verification token so it cannot be reused
      await prisma.verificationToken.delete({
        where: { token: tokenHash },
      });

      // Log successful verification with security event
      const successEvent: AuthEvent = {
        type: 'EMAIL_VERIFICATION_SUCCESS',
        timestamp: new Date(),
        userId: updatedUser.id,
        email: updatedUser.email!,
        metadata: {
          action: actionType,
          previousEmail: isEmailChange ? targetUser.email : undefined,
          ip: context?.ip,
          userAgent: context?.userAgent,
          deviceFingerprint: context?.deviceFingerprint,
        },
      };
      this.logInfo(`Email verification successful: ${actionType}`, sanitizeAuthEvent(successEvent));

      // Remove sensitive data from response
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { passwordHash, ...safeUser } = updatedUser;

      return {
        success: true,
        message: resultMessage,
        user: safeUser as any,
      };
    } catch (error) {
      this.logError('Email verification failed', {
        tokenPreview: token ? token.substring(0, 8) + '...' : 'null',
        error: sanitizeError(error),
        ip: context?.ip,
      });
      throw error;
    }
  }

  /**
   * Check if a user's email is verified
   */
  async isEmailVerified(userId: string): Promise<boolean> {
    try {
      const user = await UserRepository.findById(userId);
      if (!user) {
        throw new HttpException(404, 'User not found');
      }
      return !!user.emailVerified;
    } catch (error) {
      this.logError('Failed to check email verification status', {
        userId,
        error: sanitizeError(error),
      });
      throw error;
    }
  }

  /**
   * Get email verification status and details
   */
  async getVerificationStatus(userId: string): Promise<{
    isVerified: boolean;
    canResendAfter?: Date;
  }> {
    try {
      const user = await UserRepository.findById(userId);
      if (!user) {
        throw new HttpException(404, 'User not found');
      }

      const rateLimitCheck = EmailVerificationRateLimit.canResend(userId);
      const canResendAfter = rateLimitCheck.canResend ? undefined : rateLimitCheck.nextAllowed;

      return {
        isVerified: !!user.emailVerified,
        ...(canResendAfter && { canResendAfter }),
      };
    } catch (error) {
      this.logError('Failed to get verification status', {
        userId,
        error: sanitizeError(error),
      });
      throw error;
    }
  }

  /**
   * Clear expired verification tokens (cleanup utility)
   */
  async cleanupExpiredTokens(): Promise<{ cleaned: number }> {
    try {
      const result = await prisma.verificationToken.deleteMany({
        where: {
          expires: {
            lt: new Date(),
          },
        },
      });

      this.logInfo('Cleaned up expired verification tokens', { cleanedCount: result.count });
      return { cleaned: result.count };
    } catch (error) {
      this.logError('Failed to cleanup expired tokens', sanitizeError(error));
      throw error;
    }
  }

  /**
   * Revoke all verification tokens for a user (security measure)
   */
  async revokeVerificationTokens(userId: string, reason?: string): Promise<void> {
    try {
      const user = await UserRepository.findById(userId);
      if (user && user.email) {
        await prisma.verificationToken.deleteMany({
          where: {
            identifier: user.email,
          },
        });
      }

      const authEvent: AuthEvent = {
        type: 'AUTH_REGISTER' as any,
        timestamp: new Date(),
        userId,
        email: user?.email || 'unknown',
        metadata: {
          action: 'verification_tokens_revoked',
          reason: reason || 'manual_revocation',
        },
      };
      this.logInfo('Verification tokens revoked', sanitizeAuthEvent(authEvent));
    } catch (error) {
      this.logError('Failed to revoke verification tokens', {
        userId,
        reason,
        error: sanitizeError(error),
      });
      throw error;
    }
  }
}

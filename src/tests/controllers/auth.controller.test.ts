import request from 'supertest';
import app from '@/app';
import { AuthService } from '@/services/auth.service';
import { COOKIE_NAMES } from '@/utils/cookieUtils';
import { JwtUtils } from '@/utils/jwt';

// Mock services
jest.mock('@/services/auth.service');
jest.mock('@/utils/jwt');
jest.mock('@/services/tokenBlacklist.service');
jest.mock('@/utils/password');
jest.mock('@/services/rbac.service');

// Mock all middlewares
jest.mock('@/middlewares', () => ({
  ...jest.requireActual('@/middlewares'),
  authMiddleware: (req: any, res: any, next: any) => {
    req.user = {
      id: 'user-123',
      email: '<EMAIL>',
      role: 'USER',
      emailVerified: new Date(),
      createdAt: new Date('2025-07-22T13:39:22.692Z'),
      updatedAt: new Date('2025-07-22T13:39:22.692Z'),
    };
    next();
  },
  authorizationMiddleware: () => (req: any, res: any, next: any) => next(),
  permissionMiddleware: () => (req: any, res: any, next: any) => next(),
  resourceAccessMiddleware: () => (req: any, res: any, next: any) => next(),
  rateLimiterMiddleware: (req: any, res: any, next: any) => next(),
  validationMiddleware: () => (req: any, res: any, next: any) => next(),
  errorMiddleware: (err: any, req: any, res: any) => {
    res.status(500).json({ message: err.message });
  },
  loggerMiddleware: (req: any, res: any, next: any) => next(),
  responseLoggerMiddleware: (req: any, res: any, next: any) => next(),
  // Security middleware mocks
  securityHeaders: jest.fn((req: any, res: any, next: any) => next()),
  sanitizeInput: jest.fn((req: any, res: any, next: any) => next()),
  deviceFingerprint: jest.fn((req: any, res: any, next: any) => next()),
  detectSuspiciousActivity: jest.fn((req: any, res: any, next: any) => next()),
  validatePasswordStrength: jest.fn((req: any, res: any, next: any) => next()),
  registerRateLimit: jest.fn((req: any, res: any, next: any) => next()),
  loginRateLimit: jest.fn((req: any, res: any, next: any) => next()),
  passwordChangeRateLimit: jest.fn((req: any, res: any, next: any) => next()),
  emailVerificationRateLimit: jest.fn((req: any, res: any, next: any) => next()),
}));

const mockAuthService = AuthService as jest.Mocked<typeof AuthService>;
const mockJwtUtils = JwtUtils as jest.Mocked<typeof JwtUtils>;

describe('AuthController - Cookie-based Authentication', () => {
  const mockUser = {
    id: 'user-123',
    username: 'testuser',
    email: '<EMAIL>',
    role: 'USER' as const,
    emailVerified: null,
    name: null,
    image: null,
    failedLoginAttempts: 0,
    lockoutExpires: null,
    createdAt: new Date('2025-07-22T13:39:22.692Z'),
    updatedAt: new Date('2025-07-22T13:39:22.692Z'),
  };

  // Expected serialized user object (dates become strings when JSON serialized)
  const expectedSerializedUser = {
    id: 'user-123',
    username: 'testuser',
    email: '<EMAIL>',
    role: 'USER' as const,
    emailVerified: null,
    name: null,
    image: null,
    failedLoginAttempts: 0,
    lockoutExpires: null,
    createdAt: '2025-07-22T13:39:22.692Z',
    updatedAt: '2025-07-22T13:39:22.692Z',
  };

  const mockAuthResponse = {
    user: mockUser,
    token: 'mock-access-token',
    refreshToken: 'mock-refresh-token',
    expiresIn: '7d',
    tokenType: 'Bearer' as const,
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock JWT utility methods
    mockJwtUtils.getTokenExpiryMs.mockReturnValue({
      access: 15 * 60 * 1000, // 15 minutes
      refresh: 30 * 24 * 60 * 60 * 1000, // 30 days
    });
    mockJwtUtils.sign.mockResolvedValue('mock-access-token');
    mockJwtUtils.signRefreshToken.mockResolvedValue('mock-refresh-token');
    mockJwtUtils.verify.mockResolvedValue({ id: 'user-123', email: '<EMAIL>', role: 'USER' });
    mockJwtUtils.getTokenId.mockReturnValue('token-id-123');
  });

  describe('POST /auth/signup', () => {
    it('should register user and set cookies without returning tokens in JSON', async () => {
      (mockAuthService.signUp as jest.Mock).mockResolvedValue(mockAuthResponse);

      const registerData = {
        username: 'testuser',
        password: 'SecurePassword123!',
      };

      const response = await request(app).post('/api/v1/auth/register').send(registerData).expect(201);

      // Verify response structure - no tokens in JSON
      expect(response.body).toEqual({
        success: true,
        message: 'User registered successfully. Please check your email for verification.',
        data: {
          user: expectedSerializedUser,
          expiresIn: '7d',
          tokenType: 'Bearer',
        },
        timestamp: expect.any(String),
      });

      // Verify cookies are set
      const cookiesRaw = response.headers['set-cookie'];
      const cookies = Array.isArray(cookiesRaw) ? cookiesRaw : cookiesRaw ? [cookiesRaw] : [];
      expect(cookies).toBeDefined();
      expect(Array.isArray(cookies)).toBe(true);
      expect(cookies.some((cookie: string) => cookie.includes(COOKIE_NAMES.ACCESS_TOKEN))).toBe(true);
      expect(cookies.some((cookie: string) => cookie.includes(COOKIE_NAMES.REFRESH_TOKEN))).toBe(true);

      // Verify secure cookie attributes
      cookies.forEach((cookie: string) => {
        if (cookie.includes('Token')) {
          expect(cookie).toContain('HttpOnly');
          expect(cookie).toContain('SameSite=Strict');
        }
      });
    });
  });

  describe('POST /auth/login', () => {
    it('should login user and set cookies without returning tokens in JSON', async () => {
      mockAuthService.logIn.mockResolvedValue(mockAuthResponse);

      const loginData = {
        username: 'testuser',
        password: 'SecurePassword123!',
      };

      const response = await request(app).post('/api/v1/auth/login').send(loginData).expect(200);

      // Verify response structure - no tokens in JSON
      expect(response.body).toEqual({
        success: true,
        message: 'User logged in successfully',
        data: {
          user: expectedSerializedUser,
          expiresIn: '7d',
          tokenType: 'Bearer',
        },
        timestamp: expect.any(String),
      });

      // Verify tokens are NOT in response body
      expect(response.body.data).not.toHaveProperty('token');
      expect(response.body.data).not.toHaveProperty('refreshToken');

      // Verify cookies are set
      const cookiesRaw = response.headers['set-cookie'];
      const cookies = Array.isArray(cookiesRaw) ? cookiesRaw : cookiesRaw ? [cookiesRaw] : [];
      expect(cookies).toBeDefined();
      expect(Array.isArray(cookies)).toBe(true);
      expect(cookies.some((cookie: string) => cookie.includes(COOKIE_NAMES.ACCESS_TOKEN))).toBe(true);
      expect(cookies.some((cookie: string) => cookie.includes(COOKIE_NAMES.REFRESH_TOKEN))).toBe(true);
    });

    it('should handle login failure without setting cookies', async () => {
      mockAuthService.logIn.mockRejectedValue(new Error('Invalid credentials'));

      const loginData = {
        username: 'testuser',
        password: 'wrongpassword',
      };

      const response = await request(app).post('/api/v1/auth/login').send(loginData).expect(500);

      // Verify no cookies are set on failure
      expect(response.headers['set-cookie']).toBeUndefined();
    });
  });

  describe('POST /auth/refresh-token', () => {
    it('should refresh tokens using cookie and set new cookies', async () => {
      mockAuthService.refreshToken.mockResolvedValue(mockAuthResponse);

      const response = await request(app)
        .post('/api/v1/auth/refresh')
        .set('Cookie', [`${COOKIE_NAMES.REFRESH_TOKEN}=old-refresh-token`])
        .expect(200);

      // Verify response structure - no tokens in JSON
      expect(response.body).toEqual({
        success: true,
        message: 'Token refreshed successfully',
        data: {
          user: expectedSerializedUser,
          expiresIn: '7d',
          tokenType: 'Bearer',
        },
        timestamp: expect.any(String),
      });

      // Verify tokens are NOT in response body
      expect(response.body.data).not.toHaveProperty('token');
      expect(response.body.data).not.toHaveProperty('refreshToken');

      // Verify new cookies are set
      const cookiesRaw = response.headers['set-cookie'];
      const cookies = Array.isArray(cookiesRaw) ? cookiesRaw : cookiesRaw ? [cookiesRaw] : [];
      expect(cookies).toBeDefined();
      expect(Array.isArray(cookies)).toBe(true);
      expect(cookies.some((cookie: string) => cookie.includes(COOKIE_NAMES.ACCESS_TOKEN))).toBe(true);
      expect(cookies.some((cookie: string) => cookie.includes(COOKIE_NAMES.REFRESH_TOKEN))).toBe(true);
    });

    it('should fail when refresh token cookie is missing', async () => {
      const response = await request(app).post('/api/v1/auth/refresh').expect(400); // Expect 400 for missing refresh token

      // Just verify we get a 400 response (the exact structure may vary)
      expect(response.status).toBe(400);
    });
  });

  describe('POST /auth/logout', () => {
    it('should logout user and clear both cookies', async () => {
      mockAuthService.logout.mockResolvedValue();

      const response = await request(app)
        .post('/api/v1/auth/logout')
        .set('Cookie', [`${COOKIE_NAMES.ACCESS_TOKEN}=access-token`, `${COOKIE_NAMES.REFRESH_TOKEN}=refresh-token`])
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: 'Logged out successfully',
        timestamp: expect.any(String),
      });

      // Verify both cookies are cleared
      const cookiesRaw = response.headers['set-cookie'];
      const cookies = Array.isArray(cookiesRaw) ? cookiesRaw : cookiesRaw ? [cookiesRaw] : [];
      expect(cookies).toBeDefined();
      expect(Array.isArray(cookies)).toBe(true);

      const accessTokenCookie = cookies.find((cookie: string) => cookie.includes(COOKIE_NAMES.ACCESS_TOKEN));
      const refreshTokenCookie = cookies.find((cookie: string) => cookie.includes(COOKIE_NAMES.REFRESH_TOKEN));

      expect(accessTokenCookie).toBeDefined();
      expect(refreshTokenCookie).toBeDefined();

      // Verify cookies are cleared (expired)
      expect(accessTokenCookie).toMatch(/Expires=Thu, 01 Jan 1970|Max-Age=0/);
      expect(refreshTokenCookie).toMatch(/Expires=Thu, 01 Jan 1970|Max-Age=0/);
    });

    it('should handle logout when no cookies are present', async () => {
      mockAuthService.logout.mockResolvedValue();

      const response = await request(app).post('/api/v1/auth/logout').expect(200);

      expect(response.body.success).toBe(true);
    });
  });
});

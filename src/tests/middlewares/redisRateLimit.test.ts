import request from 'supertest';
import express from 'express';
import { HttpException } from '../../exceptions/HttpException';

// Create mock functions that we can control
const mockRateLimiter = {
  consume: jest.fn(),
  get: jest.fn(),
};

// Mock rate-limiter-flexible BEFORE importing the middleware
jest.mock('rate-limiter-flexible', () => ({
  RateLimiterRedis: jest.fn().mockImplementation(() => mockRateLimiter),
}));

// Mock Redis BEFORE importing the middleware
jest.mock('../../config/redis', () => ({
  redis: {
    setex: jest.fn(),
    get: jest.fn(),
    incr: jest.fn(),
    expire: jest.fn(),
  },
}));

// Now import the middleware after mocks are set up
import {
  loginRateLimit,
  registerRateLimit,
  passwordChangeRateLimit,
  emailVerificationRateLimit,
  generalApiRateLimit,
  strictRateLimit,
  getRateLimitStats,
} from '../../middlewares/security.middleware';
// import { RateLimiterRedis } from 'rate-limiter-flexible';

describe('Redis Rate Limiting Middleware', () => {
  let app: express.Application;

  beforeEach(() => {
    jest.clearAllMocks();

    app = express();
    app.use(express.json());

    // Error handler
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    app.use((err: any, req: any, res: any, _next: any) => {
      if (err instanceof HttpException) {
        res.status(err.status).json({ error: err.message });
      } else {
        res.status(500).json({ error: 'Internal server error' });
      }
    });
  });

  describe('Login Rate Limiting', () => {
    beforeEach(() => {
      app.post('/api/login', loginRateLimit, (req, res) => {
        res.json({ success: true, message: 'Login successful' });
      });

      // Add error handler after the route
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      app.use((err: any, req: any, res: any, _next: any) => {
        if (err instanceof HttpException) {
          res.status(err.status).json({ error: err.message });
        } else {
          res.status(500).json({ error: 'Internal server error' });
        }
      });
    });

    it('should allow login requests within rate limit', async () => {
      mockRateLimiter.consume.mockResolvedValue({});

      const response = await request(app)
        .post('/api/login')
        .send({ email: '<EMAIL>', password: 'password' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(mockRateLimiter.consume).toHaveBeenCalled();
    });

    it('should reject login requests exceeding rate limit', async () => {
      const rateLimiterError = {
        msBeforeNext: 60000,
        remainingPoints: 0,
      };
      mockRateLimiter.consume.mockRejectedValue(rateLimiterError);

      const response = await request(app)
        .post('/api/login')
        .send({ email: '<EMAIL>', password: 'password' })
        .expect(429);

      expect(response.body.error).toContain('Too many login attempts');
      expect(response.headers['retry-after']).toBe('60');
    });

    it('should use IP and email combination as rate limit key', async () => {
      mockRateLimiter.consume.mockResolvedValue({});

      await request(app).post('/api/login').send({ email: '<EMAIL>', password: 'password' });

      expect(mockRateLimiter.consume).toHaveBeenCalledWith(expect.stringContaining('<EMAIL>'));
    });

    it('should handle missing email in rate limit key', async () => {
      mockRateLimiter.consume.mockResolvedValue({});

      await request(app).post('/api/login').send({ password: 'password' });

      expect(mockRateLimiter.consume).toHaveBeenCalledWith(expect.stringContaining('unknown'));
    });
  });

  describe('Registration Rate Limiting', () => {
    beforeEach(() => {
      app.post('/api/register', registerRateLimit, (req, res) => {
        res.json({ success: true, message: 'Registration successful' });
      });

      // Add error handler after the route
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      app.use((err: any, req: any, res: any, _next: any) => {
        if (err instanceof HttpException) {
          res.status(err.status).json({ error: err.message });
        } else {
          res.status(500).json({ error: 'Internal server error' });
        }
      });
    });

    it('should allow registration requests within rate limit', async () => {
      mockRateLimiter.consume.mockResolvedValue({});

      const response = await request(app)
        .post('/api/register')
        .send({ email: '<EMAIL>', password: 'password' })
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should reject registration requests exceeding rate limit', async () => {
      const rateLimiterError = {
        msBeforeNext: 3600000,
        remainingPoints: 0,
      };
      mockRateLimiter.consume.mockRejectedValue(rateLimiterError);

      const response = await request(app)
        .post('/api/register')
        .send({ email: '<EMAIL>', password: 'password' })
        .expect(429);

      expect(response.body.error).toContain('Too many registration attempts');
      expect(response.headers['retry-after']).toBe('3600');
    });
  });

  describe('Password Change Rate Limiting', () => {
    beforeEach(() => {
      app.post('/api/change-password', passwordChangeRateLimit, (req, res) => {
        res.json({ success: true, message: 'Password changed' });
      });

      // Add error handler after the route
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      app.use((err: any, req: any, res: any, _next: any) => {
        if (err instanceof HttpException) {
          res.status(err.status).json({ error: err.message });
        } else {
          res.status(500).json({ error: 'Internal server error' });
        }
      });
    });

    it('should allow password change requests within rate limit', async () => {
      mockRateLimiter.consume.mockResolvedValue({});

      const response = await request(app)
        .post('/api/change-password')
        .send({ oldPassword: 'old', newPassword: 'new' })
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should reject password change requests exceeding rate limit', async () => {
      const rateLimiterError = {
        msBeforeNext: 1800000,
        remainingPoints: 0,
      };
      mockRateLimiter.consume.mockRejectedValue(rateLimiterError);

      const response = await request(app)
        .post('/api/change-password')
        .send({ oldPassword: 'old', newPassword: 'new' })
        .expect(429);

      expect(response.body.error).toContain('Too many password change attempts');
      expect(response.headers['retry-after']).toBe('1800');
    });
  });

  describe('Email Verification Rate Limiting', () => {
    beforeEach(() => {
      app.post('/api/verify-email', emailVerificationRateLimit, (req, res) => {
        res.json({ success: true, message: 'Email verification sent' });
      });

      // Add error handler after the route
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      app.use((err: any, req: any, res: any, _next: any) => {
        if (err instanceof HttpException) {
          res.status(err.status).json({ error: err.message });
        } else {
          res.status(500).json({ error: 'Internal server error' });
        }
      });
    });

    it('should allow email verification requests within rate limit', async () => {
      mockRateLimiter.consume.mockResolvedValue({});

      const response = await request(app).post('/api/verify-email').send({ email: '<EMAIL>' }).expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should reject email verification requests exceeding rate limit', async () => {
      const rateLimiterError = {
        msBeforeNext: 900000,
        remainingPoints: 0,
      };
      mockRateLimiter.consume.mockRejectedValue(rateLimiterError);

      const response = await request(app).post('/api/verify-email').send({ email: '<EMAIL>' }).expect(429);

      expect(response.body.error).toContain('Too many email verification requests');
      expect(response.headers['retry-after']).toBe('900');
    });
  });

  describe('General API Rate Limiting', () => {
    beforeEach(() => {
      app.get('/api/data', generalApiRateLimit, (req, res) => {
        res.json({ success: true, data: 'some data' });
      });

      // Add error handler after the route
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      app.use((err: any, req: any, res: any, _next: any) => {
        if (err instanceof HttpException) {
          res.status(err.status).json({ error: err.message });
        } else {
          res.status(500).json({ error: 'Internal server error' });
        }
      });
    });

    it('should allow API requests within rate limit', async () => {
      mockRateLimiter.consume.mockResolvedValue({});

      const response = await request(app).get('/api/data').expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should reject API requests exceeding rate limit', async () => {
      const rateLimiterError = {
        msBeforeNext: 60000,
        remainingPoints: 0,
      };
      mockRateLimiter.consume.mockRejectedValue(rateLimiterError);

      const response = await request(app).get('/api/data').expect(429);

      expect(response.body.error).toContain('Too many requests');
      expect(response.headers['retry-after']).toBe('60');
    });

    it('should use IP address as rate limit key', async () => {
      mockRateLimiter.consume.mockResolvedValue({});

      await request(app).get('/api/data');

      expect(mockRateLimiter.consume).toHaveBeenCalledWith(expect.any(String));
    });
  });

  describe('Strict Rate Limiting', () => {
    beforeEach(() => {
      app.post('/api/sensitive', strictRateLimit, (req, res) => {
        res.json({ success: true, message: 'Sensitive operation completed' });
      });

      // Add error handler after the route
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      app.use((err: any, req: any, res: any, _next: any) => {
        if (err instanceof HttpException) {
          res.status(err.status).json({ error: err.message });
        } else {
          res.status(500).json({ error: 'Internal server error' });
        }
      });
    });

    it('should allow sensitive requests within rate limit', async () => {
      mockRateLimiter.consume.mockResolvedValue({});

      const response = await request(app).post('/api/sensitive').send({ data: 'sensitive' }).expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should reject sensitive requests exceeding rate limit', async () => {
      const rateLimiterError = {
        msBeforeNext: 300000,
        remainingPoints: 0,
      };
      mockRateLimiter.consume.mockRejectedValue(rateLimiterError);

      const response = await request(app).post('/api/sensitive').send({ data: 'sensitive' }).expect(429);

      expect(response.body.error).toContain('Rate limit exceeded for sensitive operation');
      expect(response.headers['retry-after']).toBe('300');
    });

    it('should use IP and user ID combination for authenticated users', async () => {
      mockRateLimiter.consume.mockResolvedValue({});

      // Create a new app instance for this test with user middleware before the route
      const testApp = express();
      testApp.use(express.json());

      // Mock authenticated user BEFORE the route
      testApp.use((req, res, next) => {
        req.user = { id: 'user-123' };
        next();
      });

      testApp.post('/api/sensitive', strictRateLimit, (req, res) => {
        res.json({ success: true, message: 'Sensitive operation completed' });
      });

      // Add error handler
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      testApp.use((err: any, req: any, res: any, _next: any) => {
        if (err instanceof HttpException) {
          res.status(err.status).json({ error: err.message });
        } else {
          res.status(500).json({ error: 'Internal server error' });
        }
      });

      await request(testApp).post('/api/sensitive').send({ data: 'sensitive' });

      expect(mockRateLimiter.consume).toHaveBeenCalledWith(expect.stringContaining('user-123'));
    });
  });

  describe('Rate Limit Statistics', () => {
    beforeEach(() => {
      app.get('/api/rate-limit-stats', getRateLimitStats);

      // Add error handler after the route
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      app.use((err: any, req: any, res: any, _next: any) => {
        if (err instanceof HttpException) {
          res.status(err.status).json({ error: err.message });
        } else {
          res.status(500).json({ error: 'Internal server error' });
        }
      });
    });

    it('should return rate limit statistics', async () => {
      const mockStats = {
        remainingPoints: 5,
        msBeforeNext: 30000,
      };

      mockRateLimiter.get.mockResolvedValue(mockStats);

      const response = await request(app).get('/api/rate-limit-stats').expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.limits).toBeDefined();
    });

    it('should handle statistics errors gracefully', async () => {
      mockRateLimiter.get.mockRejectedValue(new Error('Redis connection failed'));

      const response = await request(app).get('/api/rate-limit-stats').expect(500);

      expect(response.body.error).toContain('Failed to retrieve rate limit statistics');
    });

    it('should return default values when no rate limit data exists', async () => {
      mockRateLimiter.get.mockResolvedValue(null);

      const response = await request(app).get('/api/rate-limit-stats').expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.limits.login.remaining).toBe(5);
      expect(response.body.data.limits.register.remaining).toBe(3);
      expect(response.body.data.limits.general.remaining).toBe(100);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    beforeEach(() => {
      app.get('/api/test', generalApiRateLimit, (req, res) => {
        res.json({ success: true });
      });

      // Add error handler after the route
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      app.use((err: any, req: any, res: any, _next: any) => {
        if (err instanceof HttpException) {
          res.status(err.status).json({ error: err.message });
        } else {
          res.status(500).json({ error: 'Internal server error' });
        }
      });
    });

    it('should handle Redis connection errors gracefully', async () => {
      mockRateLimiter.consume.mockRejectedValue(new Error('Redis connection failed'));

      const response = await request(app).get('/api/test').expect(500);

      expect(response.body.error).toContain('Internal server error');
    });

    it('should handle malformed rate limiter responses', async () => {
      mockRateLimiter.consume.mockRejectedValue({
        // Missing msBeforeNext property - should be treated as Redis connection error
        remainingPoints: 0,
      });

      const response = await request(app).get('/api/test').expect(500);

      expect(response.body.error).toContain('Internal server error');
    });

    it('should handle missing IP address', async () => {
      mockRateLimiter.consume.mockResolvedValue({});

      // Create a new app instance for this test with IP middleware before the route
      const testApp = express();
      testApp.use(express.json());

      // Mock request without IP BEFORE the route
      testApp.use((req, res, next) => {
        Object.defineProperty(req, 'ip', { value: undefined, writable: true });
        next();
      });

      testApp.get('/api/test', generalApiRateLimit, (req, res) => {
        res.json({ success: true });
      });

      // Add error handler
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      testApp.use((err: any, req: any, res: any, _next: any) => {
        if (err instanceof HttpException) {
          res.status(err.status).json({ error: err.message });
        } else {
          res.status(500).json({ error: 'Internal server error' });
        }
      });

      await request(testApp).get('/api/test').expect(200);

      expect(mockRateLimiter.consume).toHaveBeenCalledWith('unknown');
    });
  });

  describe('Distributed Rate Limiting', () => {
    it('should work across multiple server instances', async () => {
      mockRateLimiter.consume.mockResolvedValue({});

      // Create a test app with the route
      const testApp = express();
      testApp.use(express.json());
      testApp.get('/api/test', generalApiRateLimit, (req, res) => {
        res.json({ success: true });
      });

      // Add error handler
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      testApp.use((err: any, req: any, res: any, _next: any) => {
        if (err instanceof HttpException) {
          res.status(err.status).json({ error: err.message });
        } else {
          res.status(500).json({ error: 'Internal server error' });
        }
      });

      // Simulate requests from same IP across different "instances"
      const requests = Array(5)
        .fill(null)
        .map(() => request(testApp).get('/api/test'));

      const responses = await Promise.all(requests);

      responses.forEach(response => {
        expect(response.status).toBe(200);
      });

      // Should use same key for all requests from same IP
      expect(mockRateLimiter.consume).toHaveBeenCalledTimes(5);
    });

    it('should maintain separate limits for different IPs', async () => {
      mockRateLimiter.consume.mockResolvedValue({});

      // Mock different IP addresses
      const ips = ['***********', '***********', '***********'];

      for (const ip of ips) {
        const testApp = express();
        testApp.use((req, res, next) => {
          Object.defineProperty(req, 'ip', { value: ip, writable: true });
          next();
        });
        testApp.get('/api/test', generalApiRateLimit, (req, res) => {
          res.json({ success: true });
        });

        await request(testApp).get('/api/test');
      }

      // Should be called with different keys for different IPs
      expect(mockRateLimiter.consume).toHaveBeenCalledTimes(3);
    });
  });

  describe('Performance and Scalability', () => {
    beforeEach(() => {
      app.get('/api/performance-test', generalApiRateLimit, (req, res) => {
        res.json({ success: true });
      });

      // Add error handler after the route
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      app.use((err: any, req: any, res: any, _next: any) => {
        if (err instanceof HttpException) {
          res.status(err.status).json({ error: err.message });
        } else {
          res.status(500).json({ error: 'Internal server error' });
        }
      });
    });

    it('should handle high-frequency requests efficiently', async () => {
      mockRateLimiter.consume.mockResolvedValue({});

      const startTime = Date.now();

      // Make 50 concurrent requests
      const requests = Array(50)
        .fill(null)
        .map(() => request(app).get('/api/performance-test'));

      await Promise.all(requests);
      const endTime = Date.now();

      // Should complete within reasonable time
      expect(endTime - startTime).toBeLessThan(10000); // 10 seconds
      expect(mockRateLimiter.consume).toHaveBeenCalledTimes(50);
    });

    it('should not cause memory leaks with many rate limit checks', async () => {
      mockRateLimiter.consume.mockResolvedValue({});

      // Simulate many different IPs
      const requests = Array(100)
        .fill(null)
        .map((_, index) => {
          const mockApp = express();
          mockApp.use((req, res, next) => {
            Object.defineProperty(req, 'ip', { value: `192.168.1.${index}`, writable: true });
            next();
          });
          mockApp.get('/test', generalApiRateLimit, (req, res) => {
            res.json({ success: true });
          });

          return request(mockApp).get('/test');
        });

      const responses = await Promise.all(requests);

      responses.forEach(response => {
        expect(response.status).toBe(200);
      });

      expect(mockRateLimiter.consume).toHaveBeenCalledTimes(100);
    });
  });
});

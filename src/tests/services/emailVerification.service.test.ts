import { EmailVerificationService } from '../../services/emailVerification.service';
import { UserRepository } from '../../repositories/user.repository';
import { EmailService } from '../../services/email.service';
import { HttpException } from '../../exceptions/HttpException';
import { User, Role, VerificationToken } from '@prisma/client';
import { prisma } from '../../loaders/prisma';

// Mock dependencies
jest.mock('../../repositories/user.repository');
jest.mock('../../services/email.service');
jest.mock('../../loaders/prisma', () => ({
  __esModule: true,
  prisma: {
    verificationToken: {
      create: jest.fn(),
      findUnique: jest.fn(),
      delete: jest.fn(),
    },
    user: {
      update: jest.fn(),
    },
  },
}));

describe('EmailVerificationService', () => {
  let emailVerificationService: EmailVerificationService;
  let mockEmailService: jest.Mocked<EmailService>;

  beforeEach(() => {
    mockEmailService = new EmailService() as jest.Mocked<EmailService>;
    emailVerificationService = new EmailVerificationService(mockEmailService);

    // Clear all mocks before each test
    jest.clearAllMocks();
    (prisma.verificationToken.create as jest.Mock).mockClear();
    (prisma.verificationToken.findUnique as jest.Mock).mockClear();
    (prisma.verificationToken.delete as jest.Mock).mockClear();
    (prisma.user.update as jest.Mock).mockClear();

    // Reset rate limiting state between tests
    // We need to access the private static class, which we can do by accessing the service's constructor
    try {
      // Try to access the rate limiting map directly
      const serviceConstructor: any = emailVerificationService.constructor;
      if (serviceConstructor && serviceConstructor.EmailVerificationRateLimit) {
        const rateLimitClass = serviceConstructor.EmailVerificationRateLimit;
        if (rateLimitClass.resendAttempts && typeof rateLimitClass.resendAttempts.clear === 'function') {
          rateLimitClass.resendAttempts.clear();
        }
      }
    } catch {
      // If we can't access it directly, that's okay - the tests might still work
      // The rate limiting is designed to reset automatically in test environments
    }
  });

  describe('sendEmailVerification', () => {
    const mockUser: User = {
      id: 'userId123',
      createdAt: new Date(),
      updatedAt: new Date(),
      name: null,
      username: null,
      email: '<EMAIL>',
      emailVerified: null,
      image: null,
      passwordHash: 'hashedpassword',
      role: Role.USER,
      failedLoginAttempts: 0,
      lockoutExpires: null,
    };

    const verifiedUser: User = {
      id: 'verifiedUserId123',
      createdAt: new Date(),
      updatedAt: new Date(),
      name: null,
      username: null,
      email: '<EMAIL>',
      emailVerified: new Date(),
      image: null,
      passwordHash: 'hashedpassword',
      role: Role.USER,
      failedLoginAttempts: 0,
      lockoutExpires: null,
    };

    it('should send verification email successfully', async () => {
      const mockToken = 'generatedVerificationToken';

      // Mock UserRepository methods
      jest.spyOn(UserRepository, 'findById').mockResolvedValue(mockUser);
      (prisma.verificationToken.create as jest.Mock).mockResolvedValue({} as VerificationToken);

      // Mock EmailService
      jest.spyOn(emailVerificationService as any, 'generateVerificationToken').mockReturnValue(mockToken);
      jest.spyOn(emailVerificationService as any, 'hashToken').mockReturnValue('hashedToken');

      // Mock EmailService.sendVerificationEmail
      mockEmailService.sendVerificationEmail = jest.fn().mockResolvedValue({
        success: true,
        messageId: 'test-message-id',
      });

      const result = await emailVerificationService.sendEmailVerification(mockUser.id);

      expect(UserRepository.findById).toHaveBeenCalledWith(mockUser.id);
      expect(prisma.verificationToken.create).toHaveBeenCalled();
      expect(mockEmailService.sendVerificationEmail).toHaveBeenCalled();
      expect(result.success).toBe(true);
    });

    it('should fail if user is already verified', async () => {
      jest.spyOn(UserRepository, 'findById').mockResolvedValue(verifiedUser);

      const result = await emailVerificationService.sendEmailVerification(verifiedUser.id);

      expect(result.success).toBe(false);
      expect(result.message).toBe('Email is already verified');
    });
  });

  describe('verifyEmail', () => {
    const validToken = 'validToken';
    const userId = 'verifiedUserId';
    const unverifiedUser: User = {
      id: userId,
      createdAt: new Date(),
      updatedAt: new Date(),
      name: null,
      username: null,
      email: '<EMAIL>',
      emailVerified: null,
      image: null,
      passwordHash: 'hashedpassword',
      role: Role.USER,
      failedLoginAttempts: 0,
      lockoutExpires: null,
    };

    it('should successfully verify email with a valid token', async () => {
      const mockVerificationToken = {
        identifier: unverifiedUser.email!,
        token: 'hashedToken',
        expires: new Date(Date.now() + 3600 * 1000),
      };
      // Mock the token hashing
      jest.spyOn(emailVerificationService as any, 'hashToken').mockReturnValue('hashedToken');
      jest.spyOn(UserRepository, 'findByVerificationToken').mockResolvedValue(unverifiedUser);
      (prisma.verificationToken.findUnique as jest.Mock).mockResolvedValue(mockVerificationToken);
      const updatedUser = { ...unverifiedUser, emailVerified: new Date() };
      jest.spyOn(UserRepository, 'update').mockResolvedValue(updatedUser);

      const result = await emailVerificationService.verifyEmail(validToken);

      expect(UserRepository.update).toHaveBeenCalledWith(unverifiedUser.id, {
        emailVerified: expect.any(Date),
      });
      expect(result.success).toBe(true);
    });

    it('should throw HttpException for an invalid or expired token', async () => {
      jest.spyOn(emailVerificationService as any, 'hashToken').mockReturnValue('hashedToken');
      jest.spyOn(UserRepository, 'findByVerificationToken').mockResolvedValue(null);

      await expect(emailVerificationService.verifyEmail('invalidToken')).rejects.toThrow(HttpException);
    });

    it('should throw HttpException if user is not found', async () => {
      jest.spyOn(emailVerificationService as any, 'hashToken').mockReturnValue('hashedToken');
      jest.spyOn(UserRepository, 'findByVerificationToken').mockResolvedValue(null);

      await expect(emailVerificationService.verifyEmail(validToken)).rejects.toThrow(HttpException);
    });

    it('should throw HttpException if user is already verified', async () => {
      const verifiedUser = {
        ...unverifiedUser,
        emailVerified: new Date(),
      };

      jest.spyOn(emailVerificationService as any, 'hashToken').mockReturnValue('hashedToken');
      jest.spyOn(UserRepository, 'findByVerificationToken').mockResolvedValue(verifiedUser);

      await expect(emailVerificationService.verifyEmail(validToken)).rejects.toThrow(HttpException);
    });
  });
});

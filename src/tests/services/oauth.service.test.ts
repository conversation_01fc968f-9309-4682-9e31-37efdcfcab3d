import { OAuthService, OAuthProfile } from '../../services/oauth.service';
import { UserRepository } from '../../repositories/user.repository';
import { Role } from '@prisma/client';
import { HttpException } from '../../exceptions/HttpException';
import { prisma } from '../../loaders/prisma';
import { AuthService } from '../../services/auth.service';
import { JwtUtils } from '../../utils/jwt';

// Mock dependencies
jest.mock('../../repositories/user.repository');
jest.mock('../../services/auth.service');
jest.mock('../../services/sessionVersioning.service');
jest.mock('../../utils/secureLogger');
jest.mock('../../utils/jwt');
jest.mock('../../loaders/prisma', () => ({
  __esModule: true,
  prisma: {
    account: {
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      deleteMany: jest.fn(),
      findMany: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  },
}));

const mockUserRepository = UserRepository as jest.Mocked<typeof UserRepository>;
const mockPrisma = prisma as any;
const mockAuthService = AuthService as jest.Mocked<any>;
const mockJwtUtils = JwtUtils as jest.Mocked<typeof JwtUtils>;

describe('OAuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('handleOAuthCallback', () => {
    const mockProfile: OAuthProfile = {
      id: 'google123',
      provider: 'google',
      email: '<EMAIL>',
      name: 'Test User',
      picture: 'https://example.com/avatar.jpg',
      verified: true,
    };

    const mockAccessToken = 'access_token_123';
    const mockRefreshToken = 'refresh_token_123';
    const mockContext = {
      ip: '127.0.0.1',
      userAgent: 'Test Agent',
      deviceFingerprint: 'test_fingerprint',
      user: null,
      token: null,
      permissions: [],
      resources: [],
    };

    it('should authenticate existing OAuth user successfully', async () => {
      // Mock existing account
      const mockAccount = {
        id: 'account123',
        user: {
          id: 'user123',
          email: '<EMAIL>',
          name: 'Test User',
          role: Role.USER,
          emailVerified: new Date(),
        },
      };

      mockPrisma.account.findUnique.mockResolvedValue(mockAccount);
      mockPrisma.account.update.mockResolvedValue({} as any);
      mockUserRepository.update.mockResolvedValue({} as any);

      // Mock JwtUtils methods (OAuth service uses JwtUtils directly)
      mockJwtUtils.sign.mockResolvedValueOnce('jwt_token').mockResolvedValueOnce('refresh_jwt');

      // Mock AuthService sanitizeUser method (called as private method)
      (AuthService as any).sanitizeUser = jest.fn().mockReturnValue({
        ...mockAccount.user,
        passwordHash: undefined, // sanitizeUser removes passwordHash
      });

      const result = await OAuthService.handleOAuthCallback(
        mockProfile,
        mockAccessToken,
        mockRefreshToken,
        mockContext,
      );

      expect(result).toEqual({
        user: {
          ...mockAccount.user,
          passwordHash: undefined, // sanitizeUser removes passwordHash
        },
        token: 'jwt_token',
        refreshToken: 'refresh_jwt',
        expiresIn: '7d',
        tokenType: 'Bearer',
        isNewUser: false,
      });

      expect(mockPrisma.account.findUnique).toHaveBeenCalledWith({
        where: {
          provider_providerAccountId: {
            provider: 'google',
            providerAccountId: 'google123',
          },
        },
        include: { user: true },
      });
    });

    it('should link OAuth account to existing user with same email', async () => {
      // Mock no existing OAuth account
      mockPrisma.account.findUnique.mockResolvedValue(null);

      // Mock existing user with same email
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        name: 'Existing User',
        username: null,
        emailVerified: new Date(),
        image: null,
        passwordHash: null,
        role: Role.USER,
        failedLoginAttempts: 0,
        lockoutExpires: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockUserRepository.findByEmail.mockResolvedValue(mockUser);
      mockPrisma.account.create.mockResolvedValue({} as any);
      mockUserRepository.update.mockResolvedValue({} as any);

      // Mock JwtUtils methods (OAuth service uses JwtUtils directly)
      mockJwtUtils.sign.mockResolvedValueOnce('jwt_token').mockResolvedValueOnce('refresh_jwt');

      // Mock AuthService sanitizeUser method (called as private method)
      (AuthService as any).sanitizeUser = jest.fn().mockReturnValue({
        ...mockUser,
        passwordHash: undefined, // sanitizeUser removes passwordHash
      });

      const result = await OAuthService.handleOAuthCallback(
        mockProfile,
        mockAccessToken,
        mockRefreshToken,
        mockContext,
      );

      expect(result).toEqual({
        user: {
          ...mockUser,
          passwordHash: undefined, // sanitizeUser removes passwordHash
        },
        token: 'jwt_token',
        refreshToken: 'refresh_jwt',
        expiresIn: '7d',
        tokenType: 'Bearer',
        isNewUser: false,
        linkedAccount: true,
      });

      expect(mockPrisma.account.create).toHaveBeenCalledWith({
        data: {
          userId: 'user123',
          type: 'oauth',
          provider: 'google',
          providerAccountId: 'google123',
          access_token: mockAccessToken,
          refresh_token: mockRefreshToken,
          expires_at: null,
          token_type: 'Bearer',
          scope: 'profile email',
        },
      });
    });

    it('should create new user for OAuth account', async () => {
      // Mock no existing account or user
      mockPrisma.account.findUnique.mockResolvedValue(null);
      mockUserRepository.findByEmail.mockResolvedValue(null);

      const mockNewUser = {
        id: 'newuser123',
        email: '<EMAIL>',
        name: 'Test User',
        username: null,
        emailVerified: new Date(),
        image: null,
        passwordHash: null,
        role: Role.USER,
        failedLoginAttempts: 0,
        lockoutExpires: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockUserRepository.create.mockResolvedValue(mockNewUser);
      mockPrisma.account.create.mockResolvedValue({} as any);

      // Mock JwtUtils methods (OAuth service uses JwtUtils directly)
      mockJwtUtils.sign.mockResolvedValueOnce('jwt_token').mockResolvedValueOnce('refresh_jwt');

      // Mock AuthService sanitizeUser method (called as private method)
      (AuthService as any).sanitizeUser = jest.fn().mockReturnValue({
        ...mockNewUser,
        passwordHash: undefined, // sanitizeUser removes passwordHash
      });

      const result = await OAuthService.handleOAuthCallback(
        mockProfile,
        mockAccessToken,
        mockRefreshToken,
        mockContext,
      );

      expect(result).toEqual({
        user: {
          ...mockNewUser,
          passwordHash: undefined, // sanitizeUser removes passwordHash
        },
        token: 'jwt_token',
        refreshToken: 'refresh_jwt',
        expiresIn: '7d',
        tokenType: 'Bearer',
        isNewUser: true,
      });

      expect(mockUserRepository.create).toHaveBeenCalledWith({
        email: '<EMAIL>',
        name: 'Test User',
        username: undefined,
        image: 'https://example.com/avatar.jpg',
        emailVerified: expect.any(Date),
        role: Role.USER,
        passwordHash: null, // OAuth users don't have passwords
      });
    });

    it('should handle OAuth callback errors gracefully', async () => {
      mockPrisma.account.findUnique.mockRejectedValue(new Error('Database error'));

      await expect(
        OAuthService.handleOAuthCallback(mockProfile, mockAccessToken, mockRefreshToken, mockContext),
      ).rejects.toThrow('Database error');
    });

    it('should handle profile without email', async () => {
      const profileWithoutEmail: OAuthProfile = {
        id: 'github123',
        provider: 'github',
        name: 'Test User',
        username: 'testuser',
      };

      // Reset mocks to ensure clean state
      mockPrisma.account.findUnique.mockResolvedValue(null); // No existing account
      mockUserRepository.findByEmail.mockResolvedValue(null); // No existing user

      await expect(
        OAuthService.handleOAuthCallback(profileWithoutEmail, mockAccessToken, mockRefreshToken, mockContext),
      ).rejects.toThrow('Email is required for OAuth registration');
    });
  });

  describe('unlinkOAuthAccount', () => {
    it('should unlink OAuth account successfully', async () => {
      const mockUser = {
        id: 'user123',
        name: 'Test User',
        username: null,
        email: '<EMAIL>',
        emailVerified: new Date(),
        image: null,
        passwordHash: 'hashed_password',
        role: Role.USER,
        failedLoginAttempts: 0,
        lockoutExpires: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockAccounts = [
        { id: 'account1', provider: 'google' },
        { id: 'account2', provider: 'facebook' },
      ];

      mockUserRepository.findById.mockResolvedValue(mockUser);
      mockPrisma.account.findMany.mockResolvedValue(mockAccounts);
      mockPrisma.account.deleteMany.mockResolvedValue({ count: 1 });

      await OAuthService.unlinkOAuthAccount('user123', 'google');

      expect(mockPrisma.account.deleteMany).toHaveBeenCalledWith({
        where: {
          userId: 'user123',
          provider: 'google',
        },
      });
    });

    it('should prevent unlinking the only authentication method', async () => {
      const mockUser = {
        id: 'user123',
        name: 'Test User',
        username: null,
        email: '<EMAIL>',
        emailVerified: new Date(),
        image: null,
        passwordHash: null, // No password set
        role: Role.USER,
        failedLoginAttempts: 0,
        lockoutExpires: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockAccounts = [
        { id: 'account1', provider: 'google' }, // Only one OAuth account
      ];

      mockUserRepository.findById.mockResolvedValue(mockUser);
      mockPrisma.account.findMany.mockResolvedValue(mockAccounts);

      await expect(OAuthService.unlinkOAuthAccount('user123', 'google')).rejects.toThrow(HttpException);
      await expect(OAuthService.unlinkOAuthAccount('user123', 'google')).rejects.toThrow(
        'Cannot unlink the only authentication method',
      );
    });

    it('should handle user not found', async () => {
      mockUserRepository.findById.mockResolvedValue(null);

      await expect(OAuthService.unlinkOAuthAccount('nonexistent', 'google')).rejects.toThrow(HttpException);
      await expect(OAuthService.unlinkOAuthAccount('nonexistent', 'google')).rejects.toThrow('User not found');
    });
  });

  describe('getUserOAuthAccounts', () => {
    it('should return user OAuth accounts', async () => {
      const mockAccounts = [
        {
          id: 'account1',
          provider: 'google',
          providerAccountId: 'google123',
          createdAt: new Date(),
        },
        {
          id: 'account2',
          provider: 'facebook',
          providerAccountId: 'facebook456',
          createdAt: new Date(),
        },
      ];

      mockPrisma.account.findMany.mockResolvedValue(mockAccounts);

      const result = await OAuthService.getUserOAuthAccounts('user123');

      expect(result).toEqual(mockAccounts);
      expect(mockPrisma.account.findMany).toHaveBeenCalledWith({
        where: {
          userId: 'user123',
          type: 'oauth',
        },
        select: {
          id: true,
          provider: true,
          providerAccountId: true,
        },
      });
    });

    it('should return empty array for user with no OAuth accounts', async () => {
      mockPrisma.account.findMany.mockResolvedValue([]);

      const result = await OAuthService.getUserOAuthAccounts('user123');

      expect(result).toEqual([]);
    });
  });
});

import { TokenBlacklistService } from '../../services/tokenBlacklist.service';
import { JwtUtils } from '../../utils/jwt';
import { redis } from '../../config/redis';
import jwt from 'jsonwebtoken';

// Mock Redis
jest.mock('../../config/redis', () => ({
  redis: {
    setex: jest.fn(),
    get: jest.fn(),
    del: jest.fn(),
    keys: jest.fn(),
    ping: jest.fn(),
    pipeline: jest.fn(() => ({
      del: jest.fn(),
      exec: jest.fn(),
    })),
    exists: jest.fn(),
    ttl: jest.fn(),
    scan: jest.fn(),
  },
}));

// Mock JwtUtils
jest.mock('../../utils/jwt', () => ({
  JwtUtils: {
    getTokenExpiry: jest.fn(),
    decode: jest.fn(),
  },
}));

describe('TokenBlacklistService', () => {
  const mockRedis = redis as jest.Mocked<typeof redis>;
  const mockJwtUtils = JwtUtils as jest.Mocked<typeof JwtUtils>;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(Date, 'now').mockReturnValue(1640995200000); // 2022-01-01 00:00:00
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('blacklistToken', () => {
    it('should blacklist token with automatic expiration', async () => {
      const tokenId = 'test-token-id';
      const expirationTime = Date.now() + 3600000; // 1 hour from now
      const reason = 'User logout';

      mockJwtUtils.getTokenExpiry.mockReturnValue(expirationTime);
      mockRedis.setex.mockResolvedValue('OK');

      await TokenBlacklistService.blacklistToken(tokenId, reason);

      expect(mockJwtUtils.getTokenExpiry).toHaveBeenCalledWith(tokenId);
      expect(mockRedis.setex).toHaveBeenCalledWith(
        `blacklist:${tokenId}`,
        3600, // TTL in seconds
        expect.stringContaining(reason),
      );
    });

    it('should handle expired tokens gracefully', async () => {
      const tokenId = 'expired-token-id';
      const expirationTime = Date.now() - 3600000; // 1 hour ago

      mockJwtUtils.getTokenExpiry.mockReturnValue(expirationTime);
      mockRedis.setex.mockResolvedValue('OK');

      await TokenBlacklistService.blacklistToken(tokenId);

      // Should not blacklist expired tokens
      expect(mockRedis.setex).not.toHaveBeenCalled();
    });

    it('should handle Redis errors gracefully', async () => {
      const tokenId = 'test-token-id';
      const expirationTime = Date.now() + 3600000;

      mockJwtUtils.getTokenExpiry.mockReturnValue(expirationTime);
      mockRedis.setex.mockRejectedValue(new Error('Redis connection failed'));

      await expect(TokenBlacklistService.blacklistToken(tokenId)).rejects.toThrow('Redis connection failed');
    });

    it('should blacklist token without reason', async () => {
      const tokenId = 'test-token-id';
      const expirationTime = Date.now() + 3600000;

      mockJwtUtils.getTokenExpiry.mockReturnValue(expirationTime);
      mockRedis.setex.mockResolvedValue('OK');

      await TokenBlacklistService.blacklistToken(tokenId);

      expect(mockRedis.setex).toHaveBeenCalledWith(
        `blacklist:${tokenId}`,
        3600,
        expect.stringContaining('"reason":"logout"'),
      );
    });
  });

  describe('isTokenBlacklisted', () => {
    it('should return true for blacklisted token', async () => {
      const tokenId = 'blacklisted-token';
      mockRedis.get.mockResolvedValue(
        JSON.stringify({
          tokenId,
          reason: 'User logout',
          blacklistedAt: new Date().toISOString(),
        }),
      );

      const result = await TokenBlacklistService.isTokenBlacklisted(tokenId);

      expect(result).toBe(true);
      expect(mockRedis.get).toHaveBeenCalledWith(`blacklist:${tokenId}`);
    });

    it('should return false for non-blacklisted token', async () => {
      const tokenId = 'valid-token';
      mockRedis.get.mockResolvedValue(null);

      const result = await TokenBlacklistService.isTokenBlacklisted(tokenId);

      expect(result).toBe(false);
      expect(mockRedis.get).toHaveBeenCalledWith(`blacklist:${tokenId}`);
    });

    it('should handle Redis errors and return true (fail secure)', async () => {
      const tokenId = 'test-token';
      mockRedis.get.mockRejectedValue(new Error('Redis connection failed'));

      const result = await TokenBlacklistService.isTokenBlacklisted(tokenId);

      expect(result).toBe(true);
    });

    it('should handle invalid JSON in Redis', async () => {
      const tokenId = 'test-token';
      mockRedis.get.mockResolvedValue('invalid-json');

      const result = await TokenBlacklistService.isTokenBlacklisted(tokenId);

      expect(result).toBe(true); // Assume blacklisted if data exists but is invalid
    });
  });

  describe('removeFromBlacklist', () => {
    it('should remove token from blacklist', async () => {
      const tokenId = 'test-token';
      mockRedis.del.mockResolvedValue(1);

      await TokenBlacklistService.removeFromBlacklist(tokenId);

      expect(mockRedis.del).toHaveBeenCalledWith(`blacklist:${tokenId}`);
    });

    it('should handle token not in blacklist', async () => {
      const tokenId = 'non-existent-token';
      mockRedis.del.mockResolvedValue(0);

      await TokenBlacklistService.removeFromBlacklist(tokenId);

      expect(mockRedis.del).toHaveBeenCalledWith(`blacklist:${tokenId}`);
    });

    it('should handle Redis errors', async () => {
      const tokenId = 'test-token';
      mockRedis.del.mockRejectedValue(new Error('Redis connection failed'));

      await expect(TokenBlacklistService.removeFromBlacklist(tokenId)).rejects.toThrow('Redis connection failed');
    });
  });

  describe('blacklistTokens', () => {
    it('should blacklist multiple tokens', async () => {
      const tokens = ['token1', 'token2', 'token3'];
      const reason = 'Account compromised';

      mockJwtUtils.getTokenExpiry
        .mockReturnValueOnce(Date.now() + 3600000)
        .mockReturnValueOnce(Date.now() + 7200000)
        .mockReturnValueOnce(Date.now() + 1800000);

      const mockPipeline = {
        setex: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([]),
      };
      mockRedis.pipeline.mockReturnValue(mockPipeline as any);

      await TokenBlacklistService.blacklistTokens(tokens, reason);

      expect(mockPipeline.setex).toHaveBeenCalledTimes(3);
      expect(mockPipeline.exec).toHaveBeenCalled();
    });

    it('should handle empty token array', async () => {
      const tokens: string[] = [];
      const mockPipeline = {
        setex: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([]),
      };
      mockRedis.pipeline.mockReturnValue(mockPipeline as any);

      await TokenBlacklistService.blacklistTokens(tokens);

      expect(mockRedis.pipeline).toHaveBeenCalled();
      expect(mockPipeline.setex).not.toHaveBeenCalled();
      expect(mockPipeline.exec).not.toHaveBeenCalled();
    });

    it('should handle Redis errors', async () => {
      const tokens = ['token1', 'token2'];

      mockJwtUtils.getTokenExpiry.mockReturnValue(Date.now() + 3600000);
      const mockPipeline = {
        setex: jest.fn().mockReturnThis(),
        exec: jest.fn().mockRejectedValue(new Error('Redis error')),
      };
      mockRedis.pipeline.mockReturnValue(mockPipeline as any);

      // Should throw the error
      await expect(TokenBlacklistService.blacklistTokens(tokens)).rejects.toThrow('Redis error');
    });
  });

  describe('getBlacklistStats', () => {
    it('should return blacklist statistics', async () => {
      const mockKeys = ['blacklist:token1', 'blacklist:token2', 'blacklist:token3'];
      mockRedis.keys.mockResolvedValue(mockKeys);
      mockRedis.ping.mockResolvedValue('PONG');

      const stats = await TokenBlacklistService.getBlacklistStats();

      expect(stats).toEqual({
        totalBlacklistedTokens: 3,
        redisConnected: true,
      });
      expect(mockRedis.keys).toHaveBeenCalledWith('blacklist:*');
      expect(mockRedis.ping).toHaveBeenCalled();
    });

    it('should handle Redis errors in stats', async () => {
      mockRedis.keys.mockRejectedValue(new Error('Redis connection failed'));

      const stats = await TokenBlacklistService.getBlacklistStats();

      expect(stats).toEqual({
        totalBlacklistedTokens: 0,
        redisConnected: false,
      });
    });
  });

  describe('Integration with JWT tokens', () => {
    it('should work with real JWT token structure', async () => {
      const payload = {
        id: '123',
        email: '<EMAIL>',
        role: 'USER',
        jti: 'token-id-123',
      };
      // Create token for testing (not used directly in this test)
      jwt.sign(payload, 'secret', { expiresIn: '1h' });

      // Mock decode to return the payload
      mockJwtUtils.decode.mockReturnValue(payload);
      mockJwtUtils.getTokenExpiry.mockReturnValue(Date.now() + 3600000);
      mockRedis.setex.mockResolvedValue('OK');

      await TokenBlacklistService.blacklistToken(payload.jti);

      expect(mockRedis.setex).toHaveBeenCalledWith(`blacklist:${payload.jti}`, expect.any(Number), expect.any(String));
    });
  });
});

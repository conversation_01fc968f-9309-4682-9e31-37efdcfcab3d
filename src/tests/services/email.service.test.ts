import { EmailService } from '../../services/email.service';
import { BaseUserData } from '../../types/template';
import path from 'path';
import fs from 'fs/promises';

// Mock node-mailjet
jest.mock('node-mailjet', () => {
  return {
    default: jest.fn().mockImplementation(() => ({
      post: jest.fn().mockReturnValue({
        request: jest.fn().mockResolvedValue({
          body: {
            Messages: [{ MessageID: 'test-message-id-123' }],
          },
        }),
      }),
      get: jest.fn().mockReturnValue({
        request: jest.fn().mockResolvedValue({ body: { Data: [] } }),
      }),
    })),
  };
});

describe('EmailService', () => {
  let emailService: EmailService;
  let mockGetTemplate: jest.SpyInstance<any, any[]>;
  let mockProcessTemplate: jest.SpyInstance<any, any[]>;
  let mockBuildVerificationUrl: jest.SpyInstance<any, any[]>;
  let mockMailjetPost: jest.Mock;
  let mockMailjetRequest: jest.Mock;

  beforeEach(() => {
    // Setup Mailjet mocks
    mockMailjetRequest = jest.fn().mockResolvedValue({
      body: {
        Messages: [{ MessageID: 'test-message-id-123' }],
      },
    });
    mockMailjetPost = jest.fn().mockReturnValue({
      request: mockMailjetRequest,
    });

    // Create email service (constructor will use mocked Mailjet)
    emailService = new EmailService();

    // Override the mailjet instance with our mocks
    (emailService as any).mailjet = {
      post: mockMailjetPost,
      get: jest.fn().mockReturnValue({
        request: jest.fn().mockResolvedValue({ body: { Data: [] } }),
      }),
    };

    // Mock internal methods that are called by public methods
    mockGetTemplate = jest.spyOn(emailService as any, 'getTemplate');
    mockProcessTemplate = jest.spyOn(emailService as any, 'processTemplate');
    mockBuildVerificationUrl = jest.spyOn(emailService as any, 'buildVerificationUrl');

    // Default mock implementation for getTemplate
    mockGetTemplate.mockImplementation(async (templateName: string) => {
      // Use the correct template directory path as per the actual implementation
      const htmlPath = path.resolve(__dirname, `../../templates/email/${templateName}.html`);
      const textPath = path.resolve(__dirname, `../../templates/email/${templateName}.txt`);
      const html = await fs.readFile(htmlPath, 'utf-8');
      const text = await fs.readFile(textPath, 'utf-8');
      return { html, text };
    });

    // Default mock implementation for processTemplate
    mockProcessTemplate.mockImplementation((template: string, variables: any) => {
      let processed = template;
      Object.entries(variables).forEach(([key, value]) => {
        const placeholder = `{{${key}}}`;
        processed = processed.replace(new RegExp(placeholder, 'g'), value as string);
      });
      return processed;
    });

    // Default mock implementation for buildVerificationUrl
    mockBuildVerificationUrl.mockImplementation((token: string, isEmailChange: boolean) => {
      return `http://localhost:3000/auth/${isEmailChange ? 'verify-email-change' : 'verify-email'}?token=${token}`;
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('sendVerificationEmail', () => {
    it('should call Mailjet with the correct arguments and interpolated template for new user', async () => {
      const user: BaseUserData = {
        id: '1',
        email: '<EMAIL>',
        role: 'user',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      const token = 'testtoken123';
      const isEmailChange = false;

      // Mock successful email send
      mockMailjetRequest.mockResolvedValueOnce({
        body: {
          Messages: [{ MessageID: 'verification-message-id-123' }],
        },
      });

      const result = await emailService.sendVerificationEmail(user, token, isEmailChange);

      // Verify the result
      expect(result.success).toBe(true);
      expect(result.messageId).toBe('verification-message-id-123');

      // Verify Mailjet was called correctly
      expect(mockMailjetPost).toHaveBeenCalledWith('send', { version: 'v3.1' });
      expect(mockMailjetRequest).toHaveBeenCalledTimes(1);

      // Verify template loading
      expect(mockGetTemplate).toHaveBeenCalledWith('verification');

      // Verify URL building
      expect(mockBuildVerificationUrl).toHaveBeenCalledWith(token, isEmailChange);

      // Verify the request payload
      const callArgs = mockMailjetRequest.mock.calls[0][0];
      expect(callArgs.Messages).toHaveLength(1);
      expect(callArgs.Messages[0].To[0].Email).toBe(user.email);
      expect(callArgs.Messages[0].Subject).toBe('Verify Your Email Address');
    });

    it('should call Mailjet with the correct arguments and interpolated template for email change', async () => {
      const user: BaseUserData = {
        id: '1',
        email: '<EMAIL>',
        role: 'user',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      const token = 'testtoken456';
      const isEmailChange = true;

      // Mock successful email send
      mockMailjetRequest.mockResolvedValueOnce({
        body: {
          Messages: [{ MessageID: 'emailchange-message-id-456' }],
        },
      });

      const result = await emailService.sendVerificationEmail(user, token, isEmailChange);

      // Verify the result
      expect(result.success).toBe(true);
      expect(result.messageId).toBe('emailchange-message-id-456');

      // Verify Mailjet was called correctly
      expect(mockMailjetPost).toHaveBeenCalledWith('send', { version: 'v3.1' });
      expect(mockMailjetRequest).toHaveBeenCalledTimes(1);

      // Verify template loading
      expect(mockGetTemplate).toHaveBeenCalledWith('email-change');

      // Verify URL building
      expect(mockBuildVerificationUrl).toHaveBeenCalledWith(token, isEmailChange);

      // Verify the request payload
      const callArgs = mockMailjetRequest.mock.calls[0][0];
      expect(callArgs.Messages).toHaveLength(1);
      expect(callArgs.Messages[0].To[0].Email).toBe(user.email);
      expect(callArgs.Messages[0].Subject).toBe('Verify Your New Email Address');
    });

    it('should handle email sending failures gracefully', async () => {
      const user: BaseUserData = {
        id: '1',
        email: '<EMAIL>',
        role: 'user',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      const token = 'testtoken789';
      const isEmailChange = false;

      // Override the existing mock to simulate failures for all retries (default is 3)
      mockMailjetRequest
        .mockRejectedValueOnce(new Error('Mailjet API error'))
        .mockRejectedValueOnce(new Error('Mailjet API error'))
        .mockRejectedValueOnce(new Error('Mailjet API error'));

      const result = await emailService.sendVerificationEmail(user, token, isEmailChange);

      // Verify the result
      expect(result.success).toBe(false);
      // The error message might be different due to how the service handles the rejection
      expect(result.error).toBeDefined();
    });
  });

  describe('getTemplate', () => {
    it('should load and cache email templates', async () => {
      // Mock file system reads
      jest.spyOn(fs, 'readFile').mockImplementation(async (filePath: any) => {
        if (filePath.toString().endsWith('.html')) {
          return '<html>{{USER_EMAIL}}</html>';
        }
        return '{{USER_EMAIL}}';
      });

      const template = await emailService.getTemplate('verification');

      expect(template.html).toBe('<html>{{USER_EMAIL}}</html>');
      expect(template.text).toBe('{{USER_EMAIL}}');

      // Verify caching works
      const cachedTemplate = await emailService.getTemplate('verification');
      expect(template).toStrictEqual(cachedTemplate);
    });
  });

  describe('processTemplate', () => {
    it('should replace template variables correctly', () => {
      const template = 'Hello {{USER_EMAIL}}, verify at {{VERIFICATION_URL}}';
      const variables = {
        APP_NAME: 'Test App',
        USER_EMAIL: '<EMAIL>',
        VERIFICATION_URL: 'http://example.com/verify?token=123',
        VERIFICATION_TITLE: 'Email Verification',
        VERIFICATION_MESSAGE: 'Please verify your email',
        EXPIRES_IN: '48 hours',
        CURRENT_YEAR: '2025',
      };

      const processed = (emailService as any).processTemplate(template, variables);

      expect(processed).toContain('Hello <EMAIL>');
      expect(processed).toContain('verify at http://example.com/verify?token=123');
    });
  });
});

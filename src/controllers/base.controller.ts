import { NextFunction, Request, Response } from 'express';

export abstract class BaseController {
  protected catchAsync(fn: (req: Request, res: Response, next?: NextFunction) => Promise<void>) {
    return (req: Request, res: Response, next: NextFunction) => {
      fn(req, res, next).catch(next);
    };
  }

  protected sendResponse<T>(res: Response, statusCode: number, data: T, message?: string) {
    res.status(statusCode).json({
      success: true,
      message,
      data,
    });
  }

  protected sendError(res: Response, statusCode: number, message: string) {
    res.status(statusCode).json({
      success: false,
      message,
    });
  }
}

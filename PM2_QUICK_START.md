# PM2 Quick Start

## Deployment Commands

```bash
# Production deployment (builds + starts with clustering)
npm run deploy:prod

# Development deployment (builds + starts single instance)
npm run deploy:dev

# Graceful reload (zero-downtime, recommended for updates)
npm run pm2:reload:prod
npm run pm2:reload:dev
```

## Monitoring & Management

```bash
# View process status
npm run pm2:status

# View logs (real-time)
npm run pm2:logs:prod
npm run pm2:logs:dev

# Monitor resources (interactive dashboard)
npm run pm2:monitor

# Stop processes
npm run pm2:stop:prod
npm run pm2:stop:dev
```

## Configuration

- **Dev**: Single instance, 1GB memory limit, logs to `./logs/dev-*.log`
- **Prod**: Multi-instance cluster, 2GB memory limit, logs to `./logs/prod-*.log`
- **Environment files**: `.env.development.local` and `.env.production.local`
- **Health checks**: Automatic monitoring via `/health` endpoint
- **Graceful shutdown**: 5s (dev) / 10s (prod) timeout

See [PM2_DEPLOYMENT.md](./PM2_DEPLOYMENT.md) for detailed documentation.
